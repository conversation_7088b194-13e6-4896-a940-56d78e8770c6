# 牛信短信渠道集成文档

## 概述

牛信短信渠道已集成到短信发送系统中，作为第四个可选的短信发送渠道。牛信支持国际短信发送，具有完善的签名机制和高可靠性。

## 配置项

### Apollo配置项

```properties
# 牛信渠道开关
SMS_NXCLOUD_ENABLED=false

# 牛信API配置
NXCLOUD_SMS_ACCESS_KEY=你的AccessKey
NXCLOUD_SMS_ACCESS_SECRET=你的AccessSecret  
NXCLOUD_SMS_APP_KEY=你的AppKey
NXCLOUD_SMS_URL=https://api.nxcloud.com/v1/sms/mt

# 渠道优先级配置（可选）
SMS_PREFIX_CHANNEL_MAP=+86=4,+1=4,+44=4
SMS_DEFAULT_CHANNEL=4
```

### 渠道ID说明

- 1: Submail（国际短信主要渠道）
- 2: 拉力（国际短信备用渠道）
- 3: 润信（批量短信渠道）
- 4: 牛信（牛信短信渠道）**新增**

## API接口

### 1. 查看渠道状态

```http
GET /sms/channel/status
```

响应示例：
```json
{
  "channels": {
    "nxcloud": {
      "id": 4,
      "name": "牛信",
      "enabled": true,
      "description": "牛信短信渠道",
      "configStatus": "牛信短信配置完整"
    }
  },
  "availableChannels": [1, 2, 3, 4],
  "config": {
    "defaultChannel": 1,
    "defaultChannelName": "Submail"
  }
}
```

### 2. 获取推荐渠道

```http
GET /sms/channel/suggest?phone=1234567890&prefix=+1
```

### 3. 测试牛信配置

```http
POST /sms/channel/test/nxcloud
```

响应示例：
```json
{
  "success": true,
  "configValid": true,
  "configStatus": "牛信短信配置完整",
  "enabled": true,
  "message": "牛信渠道配置正常，可以发送短信"
}
```

### 4. 测试牛信模板短信

```http
POST /sms/channel/test/nxcloud/template?phone=8615088888888&code=123456&time=5&templateType=english
```

响应示例：
```json
{
  "success": true,
  "phone": "8615088888888",
  "message": "牛信模板短信发送成功",
  "variables": {
    "code": "123456",
    "time": "5"
  },
  "templateType": "english",
  "channel": "牛信",
  "timestamp": 1703123456789
}
```

### 5. 发送模板短信

```http
POST /mail/phone/send/template
```

请求体：
```json
{
  "phonePrefix": "+86",
  "phone": "15088888888",
  "useEnglishTemplate": true,
  "variables": {
    "code": "123456",
    "time": "5"
  }
}
```

## 使用方式

### 1. 启用牛信渠道

```properties
SMS_NXCLOUD_ENABLED=true
```

### 2. 配置API参数

根据牛信提供的API文档配置相应参数：

```properties
NXCLOUD_SMS_ACCESS_KEY=你的AccessKey
NXCLOUD_SMS_ACCESS_SECRET=你的AccessSecret
NXCLOUD_SMS_APP_KEY=你的AppKey
```

### 3. 设置渠道优先级（可选）

可以通过前缀映射设置特定地区号码优先使用牛信：

```properties
SMS_PREFIX_CHANNEL_MAP=+86=4,+1=4,+44=4
```

### 4. 设置为默认渠道（可选）

```properties
SMS_DEFAULT_CHANNEL=4
```

## 技术特性

### 1. 签名机制

牛信采用MD5签名机制，确保API调用的安全性：

- 签名算法：`hex(md5(headersStr + bodyStr + accessSecretStr))`
- 头部参数按ASCII码升序排列
- 时间戳允许60秒误差

### 2. 请求限制

- 验证码通道最多支持5个号码批量发送
- 短信内容最大长度1000字符
- 支持中英文模板

### 3. 错误处理

完善的错误处理机制：
- 配置验证
- 响应状态检查
- 详细的错误日志

### 4. 模板支持

内置中英文验证码模板：

**中文模板：**
```
您的验证码是：${code}，有效期${time}分钟，请勿泄露给他人。
```

**英文模板：**
```
Your verification code is: ${code}. Valid for ${time} minutes. Please do not share this code with others.
```

## 集成验证

### 1. 配置检查

使用配置检查API验证牛信配置是否正确：

```bash
curl -X POST http://localhost:8080/sms/channel/test/nxcloud
```

### 2. 发送测试

发送测试短信验证功能：

```bash
curl -X POST "http://localhost:8080/sms/channel/test/nxcloud/template?phone=8615088888888&code=123456&templateType=english"
```

### 3. 查看状态

查看所有渠道状态：

```bash
curl http://localhost:8080/sms/channel/status
```

## 监控和日志

### 1. 发送日志

所有短信发送都会记录详细日志：
- 请求参数
- 响应结果
- 处理时间
- 错误信息

### 2. 渠道选择日志

系统会记录渠道选择逻辑：
- 建议渠道
- 选择原因
- 发送结果

### 3. 配置变更日志

Apollo配置变更会实时生效并记录日志。

## 故障排除

### 1. 常见错误

**配置不完整：**
- 检查AccessKey、AccessSecret、AppKey是否正确配置
- 确认API URL是否正确

**渠道未启用：**
- 检查SMS_NXCLOUD_ENABLED配置
- 确认在Apollo中已正确设置

**签名错误：**
- 检查AccessSecret是否正确
- 确认时间戳是否在有效范围内

### 2. 调试方法

启用调试日志查看详细的签名计算过程：

```properties
logging.level.subsms.com.media.mail.NxcloudSmsHttpClient=DEBUG
```

### 3. 测试建议

1. 先使用配置检查API验证配置
2. 使用测试API发送测试短信
3. 查看日志确认发送流程
4. 检查牛信控制台确认消息状态

## 注意事项

1. **号码格式：** 必须包含国家代码（如：8615088888888）
2. **内容限制：** 短信内容不能超过1000字符
3. **批量限制：** 验证码通道单次最多发送5个号码
4. **时间戳：** 请求时间戳误差不能超过60秒
5. **IP白名单：** 确保服务器IP在牛信白名单中

## 更新历史

- **v1.0.0** (2024-12-xx): 牛信短信渠道初始版本
  - 实现基本的短信发送功能
  - 支持模板短信
  - 完整的签名机制
  - API管理接口
  - 渠道切换支持 
