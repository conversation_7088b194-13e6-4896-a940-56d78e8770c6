package com.media.mail.util;

import com.media.mail.metrics.EmailMetrics;
import com.media.mail.service.BlacklistedEmailService;
import com.media.mail.service.VerifiedEmailService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.naming.NamingException;
import javax.naming.directory.Attribute;
import javax.naming.directory.Attributes;
import javax.naming.directory.DirContext;
import javax.naming.directory.InitialDirContext;
import java.net.IDN;
import java.util.Hashtable;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;

/**
 * 邮箱地址验证工具
 * 用于在发送邮件前验证邮箱地址的有效性，减少硬退信
 */
@Component
public class EmailValidator implements InitializingBean {

    private static final Logger logger = LoggerFactory.getLogger(EmailValidator.class);

    @Autowired
    private EmailMetrics emailMetrics;

    @Autowired
    private BlacklistedEmailService blacklistedEmailService;

    @Autowired
    private VerifiedEmailService verifiedEmailService;

    @Value("${EMAIL_VALIDATION_ENABLED:true}")
    private boolean validationEnabled;

    @Value("${EMAIL_VALIDATION_CHECK_MX:true}")
    private boolean checkMxRecords;

    @Value("${EMAIL_MX_CACHE_EXPIRATION_HOURS:24}")
    private int mxCacheExpirationHours;

    // 缓存MX记录查询结果
    private final Map<String, CachedMxResult> mxRecordCache = new ConcurrentHashMap<>();

    // 邮箱格式正则表达式
    private static final Pattern EMAIL_PATTERN =
        Pattern.compile("^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,7}$");

    // 常见的临时邮箱域名，从Apollo配置中获取，默认值为常见临时邮箱
    @Value("${EMAIL_DISPOSABLE_DOMAINS:mailinator.com,tempmail.com,throwawaymail.com,yopmail.com,guerrillamail.com,sharklasers.com,10minutemail.com}")
    private String disposableDomainsString;

    // 邮箱白名单，这些域名将跳过MX记录检查和其他验证
    @Value("${EMAIL_WHITELIST_DOMAINS:gmail.com,outlook.com,hotmail.com,yahoo.com,163.com,qq.com,126.com}")
    private String whitelistDomainsString;

    private String[] disposableDomains;
    private String[] whitelistDomains;

    /**
     * 初始化方法，解析配置的临时邮箱域名字符串
     */
    @Override
    public void afterPropertiesSet() throws Exception {
        // 初始化临时邮箱域名列表
        if (disposableDomainsString != null && !disposableDomainsString.trim().isEmpty()) {
            disposableDomains = disposableDomainsString.split(",");
            // 去除空格
            for (int i = 0; i < disposableDomains.length; i++) {
                disposableDomains[i] = disposableDomains[i].trim();
            }
            logger.info("Loaded {} disposable email domains from configuration", disposableDomains.length);
        } else {
            // 默认值
            disposableDomains = new String[] {
                "mailinator.com", "tempmail.com", "throwawaymail.com", "yopmail.com",
                "guerrillamail.com", "sharklasers.com", "10minutemail.com"
            };
            logger.info("Using default disposable email domains list");
        }

        // 初始化白名单域名列表
        if (whitelistDomainsString != null && !whitelistDomainsString.trim().isEmpty()) {
            whitelistDomains = whitelistDomainsString.split(",");
            // 去除空格
            for (int i = 0; i < whitelistDomains.length; i++) {
                whitelistDomains[i] = whitelistDomains[i].trim();
            }
            logger.info("Loaded {} whitelist email domains from configuration", whitelistDomains.length);
        } else {
            // 默认值
            whitelistDomains = new String[] {
                "gmail.com", "outlook.com", "hotmail.com", "yahoo.com", "163.com", "qq.com", "126.com"
            };
            logger.info("Using default whitelist email domains list");
        }
    }

    /**
     * 验证邮箱地址是否有效
     * @param email 邮箱地址
     * @return 是否有效
     */
    public boolean isValid(String email) {
        if (!validationEnabled) {
            return true;
        }

        long startTime = System.currentTimeMillis();
        boolean valid = false;
        String reason = "unknown";

        try {
            // 基本格式验证
            if (email == null || email.trim().isEmpty()) {
                reason = "empty";
                return false;
            }

            // 格式验证
            if (!EMAIL_PATTERN.matcher(email).matches()) {
                reason = "format";
                return false;
            }

            // 提取域名部分
            String domain = email.substring(email.indexOf('@') + 1).toLowerCase();

            // 检查是否在白名单中
            for (String whitelistDomain : whitelistDomains) {
                if (domain.equals(whitelistDomain)) {
                    // 白名单域名直接通过验证
                    valid = true;
                    reason = "whitelist";
                    return true;
                }
            }

            // 检查是否是已验证的邮箱或域名
            if (verifiedEmailService.isEmailVerified(email) || verifiedEmailService.isDomainVerified(domain)) {
                valid = true;
                reason = "verified";
                return true;
            }

            // 检查是否在黑名单中
            if (blacklistedEmailService.isDomainBlacklisted(domain)) {
                reason = "blacklisted";
                return false;
            }

            // 检查是否是临时邮箱
            for (String disposableDomain : disposableDomains) {
                if (domain.equals(disposableDomain)) {
                    // 将临时邮箱域名添加到黑名单
                    blacklistedEmailService.addDomainToBlacklist(domain, "disposable");
                    reason = "disposable";
                    return false;
                }
            }

            // MX记录验证
            if (checkMxRecords) {
                valid = checkMxRecord(domain);
                if (!valid) {
                    // 将无MX记录的域名添加到黑名单，但不设置为永久
                    blacklistedEmailService.addDomainToBlacklist(domain, "no_mx_record");
                    reason = "no_mx_record";
                    return false;
                }
            }

            valid = true;
            return true;
        } finally {
            long duration = System.currentTimeMillis() - startTime;
            emailMetrics.recordEmailValidation(valid, reason, duration);

            if (!valid) {
                logger.warn("Invalid email detected: {} (reason: {})", email, reason);
            }
        }
    }

    /**
     * 检查域名是否有有效的MX记录
     * @param domain 域名
     * @return 是否有有效MX记录
     */
    private boolean checkMxRecord(String domain) {
        try {
            // 转换为ASCII域名（处理国际化域名）
            domain = IDN.toASCII(domain);

            // 检查缓存
            CachedMxResult cachedResult = mxRecordCache.get(domain);
            if (cachedResult != null && !cachedResult.isExpired()) {
                logger.debug("Using cached MX result for domain {}: {}", domain, cachedResult.isValid());
                return cachedResult.isValid();
            }

            // 执行DNS查询
            Hashtable<String, String> env = new Hashtable<>();
            env.put("java.naming.factory.initial", "com.sun.jndi.dns.DnsContextFactory");
            DirContext dirContext = new InitialDirContext(env);

            Attributes attributes = dirContext.getAttributes(domain, new String[] {"MX"});
            Attribute attribute = attributes.get("MX");

            boolean valid = (attribute != null && attribute.size() > 0);

            // 缓存结果
            mxRecordCache.put(domain, new CachedMxResult(valid, System.currentTimeMillis(), mxCacheExpirationHours));

            return valid;
        } catch (NamingException e) {
            logger.debug("Failed to check MX record for domain {}: {}", domain, e.getMessage());
            // 缓存失败结果
            mxRecordCache.put(domain, new CachedMxResult(false, System.currentTimeMillis(), mxCacheExpirationHours));
            return false;
        } catch (Exception e) {
            logger.warn("Error checking MX record for domain {}: {}", domain, e.getMessage());
            return false;
        }
    }

    /**
     * MX记录缓存结果类
     */
    private static class CachedMxResult {
        private final boolean valid;
        private final long timestamp;
        private final long expirationTimeMs;

        public CachedMxResult(boolean valid, long timestamp, int expirationHours) {
            this.valid = valid;
            this.timestamp = timestamp;
            this.expirationTimeMs = TimeUnit.HOURS.toMillis(expirationHours);
        }

        public boolean isValid() {
            return valid;
        }

        public boolean isExpired() {
            return System.currentTimeMillis() - timestamp > expirationTimeMs;
        }
    }
}
