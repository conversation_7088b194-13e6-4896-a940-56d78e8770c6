package com.media.mail.util;

import com.alibaba.fastjson.JSONObject;
import com.media.mail.bean.SendMailRequest;
import com.media.mail.subsms.RequestEncoder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Map;
import java.util.TreeMap;

/**
 * Submail邮件发送实现
 */
@Component
public class SubmailEmailSender implements EmailSender {

    private static final Logger logger = LoggerFactory.getLogger(SubmailEmailSender.class);

    private static final String SEND_URL = "https://api-v4.mysubmail.com/mail/send.json";
    private static final String XSEND_URL = "https://api-v4.mysubmail.com/mail/xsend.json";

    private static final String TYPE_MD5 = "md5";
    private static final String SIGN_VERSION = "2";

    @Value("${SUBMAIL_EMAIL_APPID:}")
    private String appId;

    @Value("${SUBMAIL_EMAIL_APPKEY:}")
    private String appKey;

    @Value("${SUBMAIL_EMAIL_FROM:}")
    private String fromEmail;

    @Value("${SUBMAIL_EMAIL_FROM_NAME:XME}")
    private String fromName;

    @Override
    public boolean sendEmail(SendMailRequest request) {
        try {
            TreeMap<String, String> requestData = new TreeMap<>();

            // 准备请求数据
            requestData.put("appid", appId);
            requestData.put("to", request.getTo());
            requestData.put("from", fromEmail);
            requestData.put("timestamp", String.valueOf(System.currentTimeMillis() / 1000));
            requestData.put("sign_type", TYPE_MD5);
            requestData.put("sign_version", "2");
            requestData.put("subject", request.getSubject());
            requestData.put("text", request.getContent());
            requestData.put("from_name", fromName);

            // 创建签名字符串：升序排列参与签名的参数
            TreeMap<String, String> signParams = new TreeMap<>();
            signParams.put("appid", requestData.get("appid"));
            signParams.put("from", requestData.get("from"));
            signParams.put("sign_type", requestData.get("sign_type"));
            signParams.put("sign_version", requestData.get("sign_version"));
            signParams.put("timestamp", requestData.get("timestamp"));
            signParams.put("to", requestData.get("to"));

            // 生成签名：appid + appkey + 参数字符串 + appid + appkey
            StringBuilder paramStr = new StringBuilder();
            for (Map.Entry<String, String> entry : signParams.entrySet()) {
                if (paramStr.length() > 0) {
                    paramStr.append("&");
                }
                paramStr.append(entry.getKey()).append("=").append(entry.getValue());
            }
            String signStr = appId + appKey + paramStr.toString() + appId + appKey;
            logger.info("生成签名:{}", signStr);
            requestData.put("signature", RequestEncoder.encode(TYPE_MD5, signStr));

            // 发送邮件
            String response = httpPost(SEND_URL, requestData);
            logger.info("Submail邮件发送响应: {}", response);

            // 检查响应是否成功
            return response != null && response.contains("\"status\":\"success\"");
        } catch (Exception e) {
            logger.error("Submail邮件发送失败", e);
            return false;
        }
    }

    @Override
    public boolean sendHtmlEmail(SendMailRequest request) {
        try {
            TreeMap<String, String> requestData = new TreeMap<>();

            // 准备请求数据
            requestData.put("appid", appId);
            requestData.put("to", request.getTo());
            requestData.put("from", fromEmail);
            requestData.put("timestamp", String.valueOf(System.currentTimeMillis() / 1000));
            requestData.put("sign_type", TYPE_MD5);
            requestData.put("sign_version", "2");
            requestData.put("subject", request.getSubject());
            requestData.put("html", request.getContent());
            requestData.put("from_name", fromName);

            // 创建签名字符串：升序排列参与签名的参数
            TreeMap<String, String> signParams = new TreeMap<>();
            signParams.put("appid", requestData.get("appid"));
            signParams.put("from", requestData.get("from"));
            signParams.put("sign_type", requestData.get("sign_type"));
            signParams.put("sign_version", requestData.get("sign_version"));
            signParams.put("timestamp", requestData.get("timestamp"));
            signParams.put("to", requestData.get("to"));

            // 生成签名：appid + appkey + 参数字符串 + appid + appkey
            StringBuilder paramStr = new StringBuilder();
            for (Map.Entry<String, String> entry : signParams.entrySet()) {
                if (paramStr.length() > 0) {
                    paramStr.append("&");
                }
                paramStr.append(entry.getKey()).append("=").append(entry.getValue());
            }
            String signStr = appId + appKey + paramStr.toString() + appId + appKey;
            logger.info("生成签名:{}", signStr);
            requestData.put("signature", RequestEncoder.encode(TYPE_MD5, signStr));

            // 发送邮件
            String response = httpPost(SEND_URL, requestData);
            logger.info("Submail HTML邮件发送响应: {}", response);

            // 检查响应是否成功
            return response != null && response.contains("\"status\":\"success\"");
        } catch (Exception e) {
            logger.error("Submail HTML邮件发送失败", e);
            return false;
        }
    }

    /**
     * 使用模板发送邮件
     * @param to 收件人邮箱
     * @param templateId 模板ID
     * @param vars 模板变量
     * @param subject 邮件主题
     * @return 是否发送成功
     */
    public boolean sendTemplateEmail(String to, String templateId, JSONObject vars, String subject) {
        try {
            TreeMap<String, String> requestData = new TreeMap<>();

            // 组合请求数据
            requestData.put("appid", appId);
            requestData.put("to", to);
            requestData.put("project", templateId);
            if (subject != null && !subject.isEmpty()) {
                requestData.put("subject", subject);
            }
            requestData.put("from", fromEmail);
            requestData.put("from_name", fromName);
            requestData.put("timestamp", String.valueOf(System.currentTimeMillis() / 1000));
            requestData.put("sign_type", TYPE_MD5);
            requestData.put("sign_version", SIGN_VERSION);

            // 添加模板变量
            if (vars != null && !vars.isEmpty()) {
                requestData.put("vars", vars.toJSONString());
            }

            // 生成签名
            String signStr = appId + appKey + RequestEncoder.formatRequest(requestData) + appId + appKey;
            logger.info("生成签名:{}", signStr);
            requestData.put("signature", RequestEncoder.encode(TYPE_MD5, signStr));

            // 发送邮件
            String response = httpPost(XSEND_URL, requestData);
            logger.info("Submail 模板邮件发送响应: {}", response);

            // 检查响应是否成功
            return response != null && response.contains("\"status\":\"success\"");
        } catch (Exception e) {
            logger.error("Submail 模板邮件发送失败", e);
            return false;
        }
    }

    /**
     * 发送HTTP POST请求
     * @param url 请求URL
     * @param data 请求数据
     * @return 响应内容
     */
    private String httpPost(String url, TreeMap<String, String> data) throws Exception {
        URL postUrl = new URL(url);
        HttpURLConnection connection = (HttpURLConnection) postUrl.openConnection();
        connection.setDoOutput(true);
        connection.setDoInput(true);
        connection.setRequestMethod("POST");
        connection.setUseCaches(false);
        connection.setInstanceFollowRedirects(true);
        connection.setRequestProperty("Content-Type", "application/json");

        connection.connect();

        OutputStreamWriter out = new OutputStreamWriter(connection.getOutputStream(), "UTF-8");
        out.write(JSONObject.toJSONString(data));
        out.flush();
        out.close();

        // 读取响应
        BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), "UTF-8"));
        StringBuilder response = new StringBuilder();
        String line;
        while ((line = reader.readLine()) != null) {
            response.append(line);
        }
        reader.close();

        return response.toString();
    }
}
