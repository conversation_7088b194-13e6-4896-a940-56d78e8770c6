package com.media.mail.util;

import com.amazonaws.regions.Regions;
import com.amazonaws.services.simpleemail.AmazonSimpleEmailService;
import com.amazonaws.services.simpleemail.AmazonSimpleEmailServiceClientBuilder;
import com.amazonaws.services.simpleemail.model.*;
import com.alibaba.fastjson.JSONObject;
import com.media.mail.bean.SendMailRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class AmazonSESSender implements EmailSender {

    private static final Logger logger = LoggerFactory.getLogger(AmazonSESSender.class);

    @Value("${SES_FROM_EMAIL:XME<<EMAIL>>}")
    private String fromEmail;

    @Value("${SES_REGION:eu-west-1}")
    private String sesRegion;

    @Value("${SES_CONFIGSET:default-profile}")
    private String configSet;

    @Override
    public boolean sendEmail(SendMailRequest req)  {
        logger.info("Send email content: {}", req.getTo());
        try {
            // Convert string region to Regions enum
            Regions region = getRegionFromString(sesRegion);
            AmazonSimpleEmailService client =
                    AmazonSimpleEmailServiceClientBuilder.standard()
                            .withRegion(region)
                            .build();
            SendEmailRequest request = new SendEmailRequest()
                    .withDestination(
                            new Destination().withToAddresses(req.getTo()))
                    .withMessage(new Message()
                            .withBody(new Body()
                                    .withText(new Content()
                                            .withCharset("UTF-8").withData(req.getContent())))
                            .withSubject(new Content()
                                    .withCharset("UTF-8").withData(req.getSubject())))
                    .withSource(fromEmail)
                    .withConfigurationSetName(configSet);
            client.sendEmail(request);
            logger.info("Email sent!");
            return true;
        } catch (Exception ex) {
            logger.info("The email was not sent. Error message: "
                    + ex.getMessage());
            return false;
        }
    }

    @Override
    public boolean sendHtmlEmail(SendMailRequest req)  {
        logger.info("Send email html: {}", req.getTo());
        try {
            Regions region = getRegionFromString(sesRegion);
            AmazonSimpleEmailService client = AmazonSimpleEmailServiceClientBuilder.standard().withRegion(region).build();
            SendEmailRequest request = new SendEmailRequest()
                    .withSource(fromEmail)
                    .withDestination(new Destination().withToAddresses(req.getTo()))
                    .withMessage(new Message()
                            .withSubject(new Content().withCharset("UTF-8").withData(req.getSubject()))
                            .withBody(new Body().withHtml(new Content().withCharset("UTF-8").withData(req.getContent()))));

            if (configSet != null && !configSet.isEmpty()) {
                request.withConfigurationSetName(configSet);
            }

            SendEmailResult result = client.sendEmail(request);
            logger.info("Email sent! Message ID: " + result.getMessageId());
            return true;
        } catch (Exception ex) {
            logger.error("The email was not sent. Error message: " + ex.getMessage());
            return false;
        }
    }

    @Override
    public boolean sendTemplateEmail(String to, String templateId, JSONObject vars, String subject) {
        try {
            // 构建模板内容
            StringBuilder htmlContent = new StringBuilder();
            htmlContent.append("<div>");

            // 这里我们需要根据模板ID获取实际的模板内容
            // 由于AWS SES没有直接支持类似Submail的模板系统，我们需要自己实现模板替换逻辑
            // 这里只是一个简单的实现，实际应用中可能需要从数据库或配置文件中加载模板

            // 假设模板内容是从某处加载的
            String templateContent = "This is a template email with variables: {{var1}}, {{var2}}";

            // 替换模板变量
            if (vars != null) {
                for (String key : vars.keySet()) {
                    templateContent = templateContent.replace("{{" + key + "}}", vars.getString(key));
                }
            }

            htmlContent.append(templateContent);
            htmlContent.append("</div>");

            // 创建发送请求
            SendEmailRequest sendRequest = new SendEmailRequest()
                    .withSource(fromEmail)
                    .withDestination(new Destination().withToAddresses(to))
                    .withMessage(new Message()
                            .withSubject(new Content().withCharset("UTF-8").withData(subject))
                            .withBody(new Body().withHtml(new Content().withCharset("UTF-8").withData(htmlContent.toString()))));

            if (configSet != null && !configSet.isEmpty()) {
                sendRequest.withConfigurationSetName(configSet);
            }

            Regions region = getRegionFromString(sesRegion);
            AmazonSimpleEmailService client = AmazonSimpleEmailServiceClientBuilder.standard().withRegion(region).build();
            SendEmailResult result = client.sendEmail(sendRequest);
            logger.info("Template email sent! Message ID: " + result.getMessageId());
            return true;
        } catch (Exception ex) {
            logger.error("The template email was not sent. Error message: " + ex.getMessage());
            return false;
        }
    }

    /**
     * Convert string region name to Regions enum
     * @param regionName the region name as a string
     * @return the corresponding Regions enum value
     */
    private Regions getRegionFromString(String regionName) {
        try {
            return Regions.valueOf(regionName.toUpperCase().replace('-', '_'));
        } catch (IllegalArgumentException e) {
            // Default to EU_WEST_1 if the region name is invalid
            logger.warn("Invalid region name: {}, using EU_WEST_1 as default", regionName);
            return Regions.EU_WEST_1;
        }
    }
}
