package com.media.mail.util;

import com.alibaba.fastjson.JSONObject;
import com.media.mail.bean.SendMailRequest;
import com.media.mail.metrics.EmailMetrics;
import com.media.mail.service.BouncedEmailService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 带有故障转移功能的邮件发送器
 * 当主要邮件服务不可用时，自动切换到备用服务
 */
@Component
public class EmailSenderWithFallback implements EmailSender {

    private static final Logger logger = LoggerFactory.getLogger(EmailSenderWithFallback.class);

    @Autowired
    private List<EmailSender> emailSenders;

    @Autowired
    private EmailMetrics emailMetrics;

    @Autowired
    private EmailValidator emailValidator;

    @Autowired
    private BouncedEmailService bouncedEmailService;

    @Value("${EMAIL_VALIDATION_ENABLED:true}")
    private boolean validationEnabled;

    @Value("${EMAIL_BLACKLIST_ENABLED:true}")
    private boolean blacklistEnabled;

    // 当前使用的发送器索引
    private final AtomicInteger currentSenderIndex = new AtomicInteger(0);

    // 连续失败次数计数
    private final AtomicInteger consecutiveFailures = new AtomicInteger(0);

    // 触发切换的连续失败次数阈值
    private static final int FAILURE_THRESHOLD = 3;

    /**
     * 设置邮件发送器列表
     * @param senders 发送器列表
     */
    public void setEmailSenders(List<EmailSender> senders) {
        this.emailSenders = senders;
        // 重置当前发送器索引，确保从第一个发送器开始
        this.currentSenderIndex.set(0);
        // 重置连续失败计数
        this.consecutiveFailures.set(0);
        logger.info("Updated email senders list, size: {}", senders.size());
    }

    @Override
    public boolean sendEmail(SendMailRequest request) {
        // 验证邮箱地址
        if (!validateEmail(request.getTo())) {
            return false;
        }

        long startTime = System.currentTimeMillis();
        boolean success = false;
        String provider = "unknown";

        try {
            EmailSender currentSender = getCurrentSender();
            provider = getProviderName(currentSender);

            success = currentSender.sendEmail(request);

            if (success) {
                // 成功后重置失败计数
                consecutiveFailures.set(0);
            } else {
                // 失败计数增加
                handleFailure(provider);
            }

            return success;
        } finally {
            long duration = System.currentTimeMillis() - startTime;
            // 收集指标
            emailMetrics.recordEmailSent(provider, "plain", success, duration, request.getTo());
        }
    }

    @Override
    public boolean sendHtmlEmail(SendMailRequest request) {
        // 验证邮箱地址
        if (!validateEmail(request.getTo())) {
            return false;
        }

        long startTime = System.currentTimeMillis();
        boolean success = false;
        String provider = "unknown";

        try {
            EmailSender currentSender = getCurrentSender();
            provider = getProviderName(currentSender);

            success = currentSender.sendHtmlEmail(request);

            if (success) {
                consecutiveFailures.set(0);
            } else {
                handleFailure(provider);
            }

            return success;
        } finally {
            long duration = System.currentTimeMillis() - startTime;
            emailMetrics.recordEmailSent(provider, "html", success, duration, request.getTo());
        }
    }

    @Override
    public boolean sendTemplateEmail(String to, String templateId, JSONObject vars, String subject) {
        // 验证邮箱地址
        if (!validateEmail(to)) {
            return false;
        }

        long startTime = System.currentTimeMillis();
        boolean success = false;
        String provider = "unknown";

        try {
            EmailSender currentSender = getCurrentSender();
            provider = getProviderName(currentSender);

            success = currentSender.sendTemplateEmail(to, templateId, vars, subject);

            if (success) {
                consecutiveFailures.set(0);
            } else {
                handleFailure(provider);
            }

            return success;
        } finally {
            long duration = System.currentTimeMillis() - startTime;
            emailMetrics.recordEmailSent(provider, "template", success, duration, to);
        }
    }

    /**
     * 获取当前使用的邮件发送器
     */
    private EmailSender getCurrentSender() {
        if (emailSenders == null || emailSenders.isEmpty()) {
            throw new IllegalStateException("No email senders available");
        }

        int index = currentSenderIndex.get() % emailSenders.size();
        return emailSenders.get(index);
    }

    /**
     * 处理发送失败情况
     * @param failedProvider 失败的提供商
     */
    private void handleFailure(String failedProvider) {
        int failures = consecutiveFailures.incrementAndGet();

        if (failures >= FAILURE_THRESHOLD) {
            // 达到阈值，切换到下一个发送器
            int currentIndex = currentSenderIndex.get();
            int nextIndex = (currentIndex + 1) % emailSenders.size();
            int next = currentSenderIndex.incrementAndGet();

            // 获取下一个提供商名称
            String nextProvider = getProviderName(emailSenders.get(nextIndex));

            logger.warn("Switching from {} to {} after {} consecutive failures. New index: {}",
                    failedProvider, nextProvider, failures, next % emailSenders.size());

            // 记录故障转移事件
            emailMetrics.recordFailover(failedProvider, nextProvider);

            // 重置失败计数
            consecutiveFailures.set(0);
        }
    }

    /**
     * 获取提供商名称用于指标收集
     */
    private String getProviderName(EmailSender sender) {
        if (sender instanceof AmazonSESSender) {
            return "aws-ses";
        } else if (sender instanceof SubmailEmailSender) {
            return "submail";
        } else {
            return sender.getClass().getSimpleName();
        }
    }

    /**
     * 验证邮箱地址
     * @param email 邮箱地址
     * @return 是否有效
     */
    private boolean validateEmail(String email) {
        // 检查黑名单
        if (blacklistEnabled && bouncedEmailService.isBlacklisted(email)) {
            logger.warn("Email {} is blacklisted, skipping send", email);
            return false;
        }

        // 验证邮箱格式
        if (validationEnabled && !emailValidator.isValid(email)) {
            logger.warn("Email {} is invalid, skipping send", email);
            return false;
        }

        return true;
    }
}
