package com.media.mail.util;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.media.mail.service.BlacklistedPhoneService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.regex.Pattern;

/**
 * 手机号验证器
 * 用于验证手机号的有效性和检查黑名单
 */
@Component
public class PhoneValidator {

    private static final Logger logger = LoggerFactory.getLogger(PhoneValidator.class);

    // 中国大陆手机号正则表达式
    private static final Pattern CN_MOBILE_PATTERN = Pattern.compile("^1[3-9]\\d{9}$");

    // 香港手机号正则表达式
    private static final Pattern HK_MOBILE_PATTERN = Pattern.compile("^(5|6|8|9)\\d{7}$");

    // 国际手机号宽松正则表达式
    private static final Pattern INTERNATIONAL_MOBILE_PATTERN = Pattern.compile("^\\d{6,15}$");

    private boolean validationEnabled = true;

    @Autowired
    private BlacklistedPhoneService blacklistedPhoneService;

    public PhoneValidator() {
        // 从Apollo获取配置
        Config config = ConfigService.getAppConfig();
        validationEnabled = config.getBooleanProperty("PHONE_VALIDATION_ENABLED", true);

        // 监听配置变更
        config.addChangeListener(changeEvent -> {
            if (changeEvent.isChanged("PHONE_VALIDATION_ENABLED")) {
                validationEnabled = config.getBooleanProperty("PHONE_VALIDATION_ENABLED", true);
                logger.info("PHONE_VALIDATION_ENABLED changed to: {}", validationEnabled);
            }
        });
    }

    /**
     * 验证手机号是否有效
     * @param phoneNumber 手机号
     * @param countryCode 国家/地区代码
     * @return 验证结果
     */
    public ValidationResult validate(String phoneNumber, String countryCode) {
        if (!validationEnabled) {
            return ValidationResult.builder()
                    .valid(true)
                    .message("Validation disabled")
                    .build();
        }

        // 标准化手机号
        phoneNumber = normalizePhoneNumber(phoneNumber);

        if (phoneNumber == null || phoneNumber.isEmpty()) {
            return ValidationResult.builder()
                    .valid(false)
                    .message("Phone number is empty")
                    .build();
        }

        // 检查黑名单
        if (blacklistedPhoneService.isBlacklisted(phoneNumber, countryCode)) {
            return ValidationResult.builder()
                    .valid(false)
                    .message("Phone number is blacklisted")
                    .build();
        }

        // 根据国家/地区代码选择不同的验证规则
        boolean formatValid = false;
        if ("86".equals(countryCode)) {
            // 中国大陆手机号
            formatValid = CN_MOBILE_PATTERN.matcher(phoneNumber).matches();
        } else if ("852".equals(countryCode)) {
            // 香港手机号
            formatValid = HK_MOBILE_PATTERN.matcher(phoneNumber).matches();
        } else {
            // 其他国际手机号
            formatValid = INTERNATIONAL_MOBILE_PATTERN.matcher(phoneNumber).matches();
        }

        if (!formatValid) {
            return ValidationResult.builder()
                    .valid(false)
                    .message("Invalid phone number format")
                    .build();
        }

        return ValidationResult.builder()
                .valid(true)
                .message("Phone number is valid")
                .build();
    }

    /**
     * 标准化手机号（去除空格、破折号等）
     * @param phoneNumber 原始手机号
     * @return 标准化后的手机号
     */
    public String normalizePhoneNumber(String phoneNumber) {
        if (phoneNumber == null) {
            return "";
        }
        // 移除所有非数字字符
        return phoneNumber.replaceAll("[^0-9]", "");
    }

    /**
     * 验证结果类
     */
    @lombok.Data
    @lombok.Builder
    public static class ValidationResult {
        private boolean valid;
        private String message;
    }
}
