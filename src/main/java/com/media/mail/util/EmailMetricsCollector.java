package com.media.mail.util;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 邮件发送指标收集器
 * 记录各邮件服务提供商的发送成功率、延迟等指标
 */
@Component
public class EmailMetricsCollector {

    private static final Logger logger = LoggerFactory.getLogger(EmailMetricsCollector.class);

    // 按提供商统计总发送次数
    private final Map<String, AtomicInteger> totalSentCount = new ConcurrentHashMap<>();

    // 按提供商统计成功发送次数
    private final Map<String, AtomicInteger> successSentCount = new ConcurrentHashMap<>();

    // 按提供商统计总延迟时间
    private final Map<String, AtomicLong> totalLatency = new ConcurrentHashMap<>();

    // 按提供商统计最大延迟时间
    private final Map<String, AtomicLong> maxLatency = new ConcurrentHashMap<>();

    // 是否启用详细日志
    private boolean enableDetailedLog = false;

    public EmailMetricsCollector() {
        // 从Apollo获取配置
        Config config = ConfigService.getAppConfig();
        enableDetailedLog = config.getBooleanProperty("EMAIL_METRICS_DETAILED_LOG", false);

        // 监听配置变更
        config.addChangeListener(changeEvent -> {
            if (changeEvent.isChanged("EMAIL_METRICS_DETAILED_LOG")) {
                enableDetailedLog = config.getBooleanProperty("EMAIL_METRICS_DETAILED_LOG", false);
                logger.info("EMAIL_METRICS_DETAILED_LOG changed to: {}", enableDetailedLog);
            }
        });
    }

    /**
     * 记录邮件发送指标
     * @param provider 邮件服务提供商
     * @param success 是否发送成功
     * @param latencyMs 发送延迟(毫秒)
     */
    public void recordEmailSent(String provider, boolean success, long latencyMs) {
        // 总发送次数+1
        totalSentCount.computeIfAbsent(provider, k -> new AtomicInteger(0)).incrementAndGet();

        // 如果成功，成功次数+1
        if (success) {
            successSentCount.computeIfAbsent(provider, k -> new AtomicInteger(0)).incrementAndGet();
        }

        // 累加延迟时间
        totalLatency.computeIfAbsent(provider, k -> new AtomicLong(0)).addAndGet(latencyMs);

        // 更新最大延迟
        maxLatency.computeIfAbsent(provider, k -> new AtomicLong(0)).updateAndGet(current -> Math.max(current, latencyMs));

        // 如果启用了详细日志，记录每次发送的详细信息
        if (enableDetailedLog) {
            logger.info("Email sent via {}: success={}, latency={}ms", provider, success, latencyMs);
        }
    }

    /**
     * 每小时打印一次统计信息
     */
    @Scheduled(fixedRate = 3600000)
    public void logHourlyStats() {
        logger.info("=== Hourly Email Sending Statistics ===");

        for (String provider : totalSentCount.keySet()) {
            int total = totalSentCount.get(provider).get();
            int success = successSentCount.getOrDefault(provider, new AtomicInteger(0)).get();

            if (total > 0) {
                double successRate = (double) success / total * 100;
                double avgLatency = (double) totalLatency.getOrDefault(provider, new AtomicLong(0)).get() / total;
                long maxLatencyValue = maxLatency.getOrDefault(provider, new AtomicLong(0)).get();

                logger.info("Provider: {}, Total: {}, Success: {}, Success Rate: {:.2f}%, Avg Latency: {:.2f}ms, Max Latency: {}ms",
                        provider, total, success, successRate, avgLatency, maxLatencyValue);
            }
        }

        logger.info("=======================================");
    }

    /**
     * 获取当前统计数据
     * @return 统计数据Map
     */
    public Map<String, Object> getCurrentStats() {
        Map<String, Object> stats = new ConcurrentHashMap<>();

        for (String provider : totalSentCount.keySet()) {
            Map<String, Object> providerStats = new ConcurrentHashMap<>();

            int total = totalSentCount.get(provider).get();
            int success = successSentCount.getOrDefault(provider, new AtomicInteger(0)).get();

            if (total > 0) {
                double successRate = (double) success / total * 100;
                double avgLatency = (double) totalLatency.getOrDefault(provider, new AtomicLong(0)).get() / total;
                long maxLatencyValue = maxLatency.getOrDefault(provider, new AtomicLong(0)).get();

                providerStats.put("total", total);
                providerStats.put("success", success);
                providerStats.put("successRate", successRate);
                providerStats.put("avgLatency", avgLatency);
                providerStats.put("maxLatency", maxLatencyValue);

                stats.put(provider, providerStats);
            }
        }

        return stats;
    }

    /**
     * 重置统计数据
     */
    public void resetStats() {
        totalSentCount.clear();
        successSentCount.clear();
        totalLatency.clear();
        maxLatency.clear();
        logger.info("Email metrics have been reset");
    }
}
