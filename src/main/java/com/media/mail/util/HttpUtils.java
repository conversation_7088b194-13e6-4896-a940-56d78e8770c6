package com.media.mail.util;

import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.entity.StringEntity;
import org.apache.http.client.HttpClient;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.net.HttpURLConnection;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
public class HttpUtils {

    /**
     * 使用application/x-www-form-urlencoded发送post请求
     *
     * @param url
     * @param paramMap
     * @return
     */
    public static String postWithParamsForString(String url, Map<String, String> paramMap) {
        List<NameValuePair> params = new ArrayList<>();
        HttpClient client = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost(url);
        String msg = "";
        try {
            if (paramMap != null && !paramMap.isEmpty()) {
                for (Map.Entry<String, String> entry : paramMap.entrySet()) {
                    params.add(new NameValuePair() {
                        @Override
                        public String getName() {
                            return entry.getKey();
                        }

                        @Override
                        public String getValue() {
                            return entry.getValue();
                        }
                    });
                }
            }
            httpPost.setEntity(new UrlEncodedFormEntity(params, StandardCharsets.UTF_8));
            httpPost.setHeader("Content-type", "application/x-www-form-urlencoded");
            HttpResponse response = client.execute(httpPost);
            int statusCode = response.getStatusLine().getStatusCode();

            if (HttpURLConnection.HTTP_OK == statusCode) {
                HttpEntity entity = response.getEntity();
                msg = EntityUtils.toString(entity);
            }
            log.info("postWithParamsForString statusCode:{},msg:{}, paramMap = {},url = {} ", statusCode, msg,JSON.toJSONString(paramMap),url);
        } catch (Exception e) {
            // 记录日志
            log.error("HttpUtils postWithParamsForString error, url = {}, paramMap = {}, error = {}", url, JSON.toJSONString(paramMap), ExceptionUtils.getStackTrace(e));
        }
        return msg;
    }

    public static String postJson(String url, String jsonBody) {
        HttpClient client = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost(url);
        String msg = "";
        try {
            StringEntity entity = new StringEntity(jsonBody, StandardCharsets.UTF_8);
            httpPost.setEntity(entity);
            httpPost.setHeader("Content-Type", "application/json");
            HttpResponse response = client.execute(httpPost);
            int statusCode = response.getStatusLine().getStatusCode();

            if (HttpURLConnection.HTTP_OK == statusCode) {
                HttpEntity respEntity = response.getEntity();
                msg = EntityUtils.toString(respEntity, StandardCharsets.UTF_8);
            }
            log.info("postJson statusCode:{},msg:{}, body = {},url = {} ", statusCode, msg, jsonBody, url);
        } catch (Exception e) {
            log.error("HttpUtils postJson error, url = {}, body = {}, error = {}", url, jsonBody, ExceptionUtils.getStackTrace(e));
        }
        return msg;
    }
}
