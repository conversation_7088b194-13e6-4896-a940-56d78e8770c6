package com.media.mail.util;

import com.alibaba.fastjson.JSONObject;
import com.media.mail.bean.SendMailRequest;

/**
 * 邮件发送接口
 */
public interface EmailSender {

    /**
     * 发送普通邮件
     * @param request 邮件请求
     * @return 是否发送成功
     */
    boolean sendEmail(SendMailRequest request);

    /**
     * 发送HTML邮件
     * @param request 邮件请求（content为HTML内容）
     * @return 是否发送成功
     */
    boolean sendHtmlEmail(SendMailRequest request);

    /**
     * 使用模板发送邮件
     * @param to 收件人邮箱
     * @param templateId 模板ID
     * @param vars 模板变量
     * @param subject 邮件主题
     * @return 是否发送成功
     */
    default boolean sendTemplateEmail(String to, String templateId, JSONObject vars, String subject) {
        // 默认实现返回false，子类可以重写此方法
        return false;
    }
}
