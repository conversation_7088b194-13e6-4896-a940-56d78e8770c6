package com.media.mail.util;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 邮件发送服务工厂，根据配置动态选择邮件服务提供商
 * 支持故障转移功能
 */
@Component
public class EmailSenderFactory implements InitializingBean {

    private static final Logger logger = LoggerFactory.getLogger(EmailSenderFactory.class);

    @Value("${EMAIL_PROVIDER:aws}")
    private String emailProvider;

    @Value("${EMAIL_FALLBACK_ENABLED:false}")
    private boolean fallbackEnabled;

    @Autowired
    private AmazonSESSender amazonSESSender;

    @Autowired
    private SubmailEmailSender submailEmailSender;

    @Autowired(required = false)
    private EmailSenderWithFallback emailSenderWithFallback;

    private volatile List<EmailSender> currentSenders = new ArrayList<>();

    /**
     * 获取当前配置的邮件发送服务
     * @return 邮件发送服务实例
     */
    public EmailSender getEmailSender() {
        // 如果启用了故障转移，使用带有故障转移功能的发送器
        if (fallbackEnabled && emailSenderWithFallback != null) {
            logger.info("Using email sender with fallback capability");
            emailSenderWithFallback.setEmailSenders(getEmailSenders());
            return emailSenderWithFallback;
        }

        // 否则使用当前列表中的第一个发送器
        List<EmailSender> senders = getEmailSenders();
        if (!senders.isEmpty()) {
            EmailSender primarySender = senders.get(0);
            logger.info("Using {} as primary email provider",
                primarySender instanceof SubmailEmailSender ? "Submail" : "AWS SES");
            return primarySender;
        }

        // 如果列表为空（不应该发生），使用默认的AWS SES
        logger.warn("No email senders available, using AWS SES as fallback");
        return amazonSESSender;
    }

    /**
     * 创建邮件发送器列表，用于故障转移
     * 按照优先级顺序排列
     */
    @Override
    public void afterPropertiesSet() {
        // 初始化时设置发送器列表
        updateSendersList();

        // 监听配置变化
        Config config = ConfigService.getAppConfig();
        config.addChangeListener(changeEvent -> {
            String changedKey = "EMAIL_PRIMARY_PROVIDER";
            if (changeEvent.isChanged(changedKey)) {
                logger.info("检测到邮件提供商配置变更，正在更新发送器列表");
                updateSendersList();
            }
        });
    }

    /**
     * 获取当前的邮件发送器列表
     */
    public List<EmailSender> getEmailSenders() {
        return currentSenders;
    }

    /**
     * 更新发送器列表
     */
    private synchronized void updateSendersList() {
        List<EmailSender> senders = new ArrayList<>();

        // 从配置中读取优先级顺序
        Config config = ConfigService.getAppConfig();
        String primaryProvider = config.getProperty("EMAIL_PRIMARY_PROVIDER", emailProvider);

        // 根据优先级添加发送器
        if ("submail".equalsIgnoreCase(primaryProvider)) {
            senders.add(submailEmailSender);
            senders.add(amazonSESSender);
        } else {
            senders.add(amazonSESSender);
            senders.add(submailEmailSender);
        }

        // 原子性更新当前发送器列表
        currentSenders = senders;
        logger.info("邮件发送器列表已更新，主发送器: {}", primaryProvider);
    }
}
