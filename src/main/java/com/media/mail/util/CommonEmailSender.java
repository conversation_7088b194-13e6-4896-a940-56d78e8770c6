package com.media.mail.util;

import com.alibaba.fastjson2.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.mail.*;
import javax.mail.internet.*;
import java.util.Arrays;
import java.util.Properties;


@Component
public class CommonEmailSender implements InitializingBean {

    private static final Logger logger = LoggerFactory.getLogger(CommonEmailSender.class);

    private String[] emails;
    private String[] passwords;
    private int[] emailCounts;

    @Value("${COMMON_EMAILS:<EMAIL>}")
    private String commonEmailsStr;

    @Value("${COMMON_PASSWORDS:}")
    private String commonPasswordsStr;

    @Value("${GMAIL_SMTP_SERVER:smtp.gmail.com}")
    private String gmailSmtpServer;

    @Value("${GMAIL_SMTP_PORT:465}")
    private String gmailSmtpPort;

    @Value("${SES_SMTP_USERNAME:}")
    private String sesSmtpUsername;

    @Value("${SES_SMTP_PASSWORD:}")
    private String sesSmtpPassword;

    public CommonEmailSender() {
        // Constructor is empty now, initialization happens in afterPropertiesSet
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        // Initialize from Apollo injected values
        emails = commonEmailsStr.split(",");
        passwords = commonPasswordsStr.split(",");
        emailCounts = new int[emails.length];
        logger.info("emails initialized from Apollo config");
    }

    public void sendEmail(String to, String subject, String content) throws MessagingException {
        for (int i = 0; i < emails.length; i++) {
            if (emailCounts[i] < 10000) {
                int finalI = i;
                new Thread(() -> {
                    try {
                        sendFromEmail(finalI, to, subject, content);
                    } catch (MessagingException e) {
                        throw new RuntimeException(e);
                    }
                }).start();
                emailCounts[i]++;
                return;
            }
        }
        throw new MessagingException("All email accounts have reached their daily limit");
    }

    private void sendFromEmail(int index, String to, String subject, String content) throws MessagingException {
        String from = emails[index];
        String password = passwords[index];

        logger.info("send mail from ={}", from);

        Properties props = new Properties();
        props.put("mail.smtp.auth", "true");
        //props.put("mail.smtp.starttls.enable", "true");
        props.put("mail.smtp.ssl.enable", "true");
        props.put("mail.smtp.host", gmailSmtpServer);
        props.put("mail.smtp.port", gmailSmtpPort);

        Session session = Session.getInstance(props, new javax.mail.Authenticator() {
            protected PasswordAuthentication getPasswordAuthentication() {
                return new PasswordAuthentication(from, password);
            }
        });

        Message message = new MimeMessage(session);
        message.setFrom(new InternetAddress(from));
        message.setRecipients(Message.RecipientType.TO, InternetAddress.parse(to));
        message.setSubject(subject);
        message.setText(content);

        Transport.send(message);
    }

    public void resetEmailCounts() {
        Arrays.fill(emailCounts, 0);
    }

    public JSONObject info() {
        JSONObject result = new JSONObject();
        for (int i = 0; i < emails.length; i++) {
            String from = emails[i];
            result.put(from, emailCounts[i]);
        }
        return result;
    }
}
