package com.media.mail.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.util.Date;

/**
 * 已验证邮箱实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("verified_email")
public class VerifiedEmail {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 邮箱地址
     */
    @TableField("email")
    private String email;

    /**
     * 域名
     */
    @TableField("domain")
    private String domain;

    /**
     * 验证来源
     */
    @TableField("source")
    private String source;

    /**
     * 首次验证时间
     */
    @TableField("verified_at")
    private Date verifiedAt;

    /**
     * 最后一次验证时间
     */
    @TableField("last_verified_at")
    private Date lastVerifiedAt;

    /**
     * 验证次数
     */
    @TableField("verify_count")
    private Integer verifyCount;

    /**
     * 过期时间
     */
    @TableField("expires_at")
    private Date expiresAt;

    /**
     * 逻辑删除标志
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    /**
     * 检查验证是否已过期
     * @return 是否已过期
     */
    public boolean isExpired() {
        return expiresAt != null && expiresAt.before(new Date());
    }
}
