package com.media.mail.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.util.Date;

/**
 * 黑名单手机号实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("blacklisted_phone")
public class BlacklistedPhone {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 手机号
     */
    @TableField("phone_number")
    private String phoneNumber;

    /**
     * 国家/地区代码
     */
    @TableField("country_code")
    private String countryCode;

    /**
     * 添加原因
     */
    @TableField("reason")
    private String reason;

    /**
     * 添加时间
     */
    @TableField("created_at")
    private Date createdAt;

    /**
     * 是否永久拉黑
     */
    @TableField("permanent")
    private Boolean permanent;

    /**
     * 过期时间（如果不是永久拉黑）
     */
    @TableField("expires_at")
    private Date expiresAt;

    /**
     * 创建者
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 备注
     */
    @TableField("notes")
    private String notes;

    /**
     * 失败次数
     */
    @TableField("failure_count")
    private Integer failureCount;

    /**
     * 逻辑删除标志
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    /**
     * 检查黑名单是否已过期
     * @return 是否已过期
     */
    public boolean isExpired() {
        if (permanent != null && permanent) {
            return false;
        }

        return expiresAt != null && expiresAt.before(new Date());
    }

    /**
     * 增加失败计数
     */
    public void incrementFailureCount() {
        if (this.failureCount == null) {
            this.failureCount = 1;
        } else {
            this.failureCount++;
        }
    }
}
