package com.media.mail.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.util.Date;

/**
 * 短信发送统计信息实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("sms_send_stats")
public class SmsSendStats {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 短信提供商
     */
    @TableField("provider")
    private String provider;

    /**
     * 短信类型（中文/英文/模板）
     */
    @TableField("sms_type")
    private String smsType;

    /**
     * 发送状态
     */
    @TableField("status")
    private String status;

    /**
     * 接收者手机号
     */
    @TableField("recipient")
    private String recipient;

    /**
     * 国家/地区代码
     */
    @TableField("country_code")
    private String countryCode;

    /**
     * 发送时间
     */
    @TableField("send_time")
    private Date sendTime;

    /**
     * 处理时间（毫秒）
     */
    @TableField("processing_time")
    private Long processingTime;

    /**
     * 重试次数
     */
    @TableField("retry_count")
    private Integer retryCount;

    /**
     * 错误信息
     */
    @TableField("error_message")
    private String errorMessage;

    /**
     * 短信内容长度（字符数）
     */
    @TableField("content_length")
    private Integer contentLength;

    /**
     * 短信ID
     */
    @TableField("message_id")
    private String messageId;

    /**
     * 模板ID
     */
    @TableField("template_id")
    private String templateId;

    /**
     * 逻辑删除标志
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    /**
     * 创建时间
     */
    @TableField("created_at")
    private Date createdAt;

    /**
     * 更新时间
     */
    @TableField("updated_at")
    private Date updatedAt;
}
