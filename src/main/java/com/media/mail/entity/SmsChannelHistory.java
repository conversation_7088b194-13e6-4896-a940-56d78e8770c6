package com.media.mail.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("sms_channel_history")
public class SmsChannelHistory {
    @TableId(type = IdType.AUTO)
    private Long id;

    private String phone;

    /**
     * 手机号前缀，例如+86,+1等
     */
    private String prefix;

    /**
     * 通道类型
     * 1: Submail
     * 2: 拉力
     * 3: 润信
     */
    private Integer channelType;

    private LocalDateTime sendTime;
    private Boolean success;
}
