package com.media.mail.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.util.Date;

/**
 * 邮件发送统计信息实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("email_send_stats")
public class EmailSendStats {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 邮件提供商
     */
    @TableField("provider")
    private String provider;

    /**
     * 邮件类型
     */
    @TableField("email_type")
    private String emailType;

    /**
     * 发送状态
     */
    @TableField("status")
    private String status;

    /**
     * 接收者邮箱
     */
    @TableField("recipient")
    private String recipient;

    /**
     * 发送者邮箱
     */
    @TableField("sender")
    private String sender;

    /**
     * 邮件主题
     */
    @TableField("subject")
    private String subject;

    /**
     * 发送时间
     */
    @TableField("send_time")
    private Date sendTime;

    /**
     * 处理时间（毫秒）
     */
    @TableField("processing_time")
    private Long processingTime;

    /**
     * 重试次数
     */
    @TableField("retry_count")
    private Integer retryCount;

    /**
     * 错误信息
     */
    @TableField("error_message")
    private String errorMessage;

    /**
     * 邮件大小（字节）
     */
    @TableField("message_size")
    private Long messageSize;

    /**
     * 邮件ID
     */
    @TableField("message_id")
    private String messageId;

    /**
     * 逻辑删除标志
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    /**
     * 创建时间
     */
    @TableField("created_at")
    private Date createdAt;

    /**
     * 更新时间
     */
    @TableField("updated_at")
    private Date updatedAt;
}
