package com.media.mail.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.util.Date;

/**
 * 黑名单邮箱实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("blacklisted_email")
public class BlacklistedEmail {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 邮箱地址
     */
    @TableField("email")
    private String email;

    /**
     * 添加原因
     */
    @TableField("reason")
    private String reason;

    /**
     * 添加时间
     */
    @TableField("created_at")
    private Date createdAt;

    /**
     * 是否永久拉黑
     */
    @TableField("permanent")
    private Boolean permanent;

    /**
     * 过期时间（如果不是永久拉黑）
     */
    @TableField("expires_at")
    private Date expiresAt;

    /**
     * 创建者
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 备注
     */
    @TableField("notes")
    private String notes;

    /**
     * 退信次数
     */
    @TableField("bounce_count")
    private Integer bounceCount;

    /**
     * 逻辑删除标志
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    /**
     * 检查黑名单是否已过期
     * @return 是否已过期
     */
    public boolean isExpired() {
        if (permanent != null && permanent) {
            return false;
        }

        return expiresAt != null && expiresAt.before(new Date());
    }

    /**
     * 增加退信计数
     */
    public void incrementBounceCount() {
        if (this.bounceCount == null) {
            this.bounceCount = 1;
        } else {
            this.bounceCount++;
        }
    }
}
