package com.media.mail.bean;

import lombok.Data;
import java.util.Map;

/**
 * 模板短信发送请求
 */
@Data
public class SendSmsTemplateRequest {

    /**
     * 电话国家前缀，例如 +86
     */
    private String phonePrefix;

    /**
     * 手机号码
     */
    private String phone;

    /**
     * 是否使用英文模板，默认为false（使用中文模板）
     */
    private boolean useEnglishTemplate = false;

    /**
     * 模板变量，key为变量名，value为变量值
     */
    private Map<String, String> variables;
}
