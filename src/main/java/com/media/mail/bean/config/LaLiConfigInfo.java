package com.media.mail.bean.config;

import lombok.Data;

import java.io.Serializable;
import java.util.Map;

@Data
public class LaLiConfigInfo implements Serializable {

    /**
     * Product sp_id 582117
     */
    public String spId;

    /**
     * 加密的id 1e3139bf66d26ab300a85bd6300c82fc
     */
    public String password;

    /**
     * http://47.242.44.254:9511/api/send-sms-single
     */
    public String sendSmsSingleUrl;

    /**
     * 不同业务对应的发送模板
     */
    public Map<String, String> businessContentMap;
}
