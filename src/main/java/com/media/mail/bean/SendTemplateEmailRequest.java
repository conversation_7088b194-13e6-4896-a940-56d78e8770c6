package com.media.mail.bean;

import com.alibaba.fastjson.JSONObject;

/**
 * 模板邮件发送请求
 */
public class SendTemplateEmailRequest {

    /**
     * 收件人邮箱
     */
    private String to;

    /**
     * 邮件主题
     */
    private String subject;

    /**
     * 模板ID
     */
    private String templateId;

    /**
     * 模板变量
     */
    private JSONObject variables;

    public String getTo() {
        return to;
    }

    public void setTo(String to) {
        this.to = to;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getTemplateId() {
        return templateId;
    }

    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }

    public JSONObject getVariables() {
        return variables;
    }

    public void setVariables(JSONObject variables) {
        this.variables = variables;
    }

    @Override
    public String toString() {
        return "SendTemplateEmailRequest{" +
                "to='" + to + '\'' +
                ", subject='" + subject + '\'' +
                ", templateId='" + templateId + '\'' +
                ", variables=" + variables +
                '}';
    }
}
