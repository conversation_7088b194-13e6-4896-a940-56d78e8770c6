package com.media.mail.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.util.StringUtils;
import software.amazon.awssdk.services.sqs.SqsAsyncClient;

import java.net.URI;

/**
 * AWS SQS V2 配置类
 * 使用 AWS SDK v2 的异步客户端实现真正的事件驱动模式
 */
@Configuration
@ConditionalOnProperty(prefix = "aws.sqs", name = "enabled", havingValue = "true", matchIfMissing = true)
public class AwsSqsV2Config {

    private static final Logger logger = LoggerFactory.getLogger(AwsSqsV2Config.class);

    @Value("${aws.sqs.region:eu-west-1}")
    private String region;

    @Value("${aws.sqs.access-key:}")
    private String accessKey;

    @Value("${aws.sqs.secret-key:}")
    private String secretKey;

    @Value("${aws.sqs.endpoint-override:}")
    private String endpointOverride;

    /**
     * 配置 SQS 异步客户端
     * 使用 AWS SDK v2 的异步非阻塞 API
     */
    @Bean
    @Primary
    public SqsAsyncClient sqsAsyncClient() {
        logger.info("Initializing AWS SQS v2 async client with region: {}", region);

        // 创建SqsAsyncClient构建器
        software.amazon.awssdk.services.sqs.SqsAsyncClientBuilder builder =
                software.amazon.awssdk.services.sqs.SqsAsyncClient.builder()
                .region(software.amazon.awssdk.regions.Region.of(region));

        // 如果提供了访问凭证，则使用静态凭证提供者
        if (StringUtils.hasText(accessKey) && StringUtils.hasText(secretKey)) {
            logger.info("Using explicit AWS credentials");
            software.amazon.awssdk.auth.credentials.AwsBasicCredentials awsCredentials =
                    software.amazon.awssdk.auth.credentials.AwsBasicCredentials.create(accessKey, secretKey);
            builder.credentialsProvider(
                    software.amazon.awssdk.auth.credentials.StaticCredentialsProvider.create(awsCredentials));
        } else {
            logger.info("Using default AWS credential provider chain");
        }

        // 如果提供了端点覆盖，则使用自定义端点（用于本地测试或特殊配置）
        if (StringUtils.hasText(endpointOverride)) {
            logger.info("Using endpoint override: {}", endpointOverride);
            builder.endpointOverride(URI.create(endpointOverride));
        }

        return builder.build();
    }
}
