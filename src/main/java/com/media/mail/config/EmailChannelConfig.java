package com.media.mail.config;

import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfigChangeListener;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 邮件通道配置
 * 采用主备容灾模式，默认使用Submail为主通道，AWS SES为备用通道
 */
@Slf4j
@Data
@Component
public class EmailChannelConfig {
    /**
     * 是否启用Submail通道（主通道）
     */
    @Value("${EMAIL_SUBMAIL_ENABLED:true}")
    private boolean submailEnabled;

    /**
     * 是否启用AWS SES通道（备用通道）
     */
    @Value("${EMAIL_SES_ENABLED:true}")
    private boolean sesEnabled;

    /**
     * 获取当前可用通道
     * @return 1: Submail, 2: AWS SES
     */
    public int getCurrentChannel() {
        return submailEnabled ? 1 : 2;
    }

    /**
     * 获取备用通道
     * @return 1: Submail, 2: AWS SES
     */
    public int getFallbackChannel() {
        return sesEnabled ? 2 : 1;
    }

    @ApolloConfigChangeListener
    public void onChange(ConfigChangeEvent changeEvent) {
        if (changeEvent.isChanged("EMAIL_SUBMAIL_ENABLED")) {
            submailEnabled = Boolean.parseBoolean(changeEvent.getChange("EMAIL_SUBMAIL_ENABLED").getNewValue());
            log.info("Submail通道（主通道）状态更新为: {}", submailEnabled);
        }
        if (changeEvent.isChanged("EMAIL_SES_ENABLED")) {
            sesEnabled = Boolean.parseBoolean(changeEvent.getChange("EMAIL_SES_ENABLED").getNewValue());
            log.info("SES通道（备用通道）状态更新为: {}", sesEnabled);
        }
    }
}
