package com.media.mail.config;

import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfigChangeListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * 短信渠道配置
 * 支持多个短信渠道：1-Submail, 2-拉力, 3-润信, 4-牛信
 * 支持通道与国家/地区兼容性配置
 */
@Slf4j
@Component
public class SmsChannelConfig {

    /**
     * Submail渠道是否启用
     */
    @Value("${SMS_SUBMAIL_ENABLED:true}")
    private boolean submailEnabled;

    /**
     * 拉力渠道是否启用
     */
    @Value("${SMS_LALI_ENABLED:true}")
    private boolean laliEnabled;

    /**
     * 润信渠道是否启用
     */
    @Value("${SMS_RUNXIN_ENABLED:false}")
    private boolean runxinEnabled;

    /**
     * 牛信渠道是否启用
     */
    @Value("${SMS_NXCLOUD_ENABLED:false}")
    private boolean nxcloudEnabled;

    /**
     * 振亿渠道是否启用
     */
    @Value("${SMS_ZHENYI_ENABLED:false}")
    private boolean zhenyiEnabled;

    /**
     * 默认通道 (1: Submail, 2: 拉力, 3: 润信, 4: 牛信, 5: 振亿)
     */
    @Value("${SMS_DEFAULT_CHANNEL:1}")
    private int defaultChannel;

    /**
     * 通道切换时间窗口 (分钟)
     */
    @Value("${SMS_SWITCH_WINDOW_MINUTES:15}")
    private int switchWindowMinutes;

    /**
     * 前缀优先通道配置，格式：前缀=通道ID
     * 例如：
     * +1=2 (美国号码优先使用拉力)
     * +44=1 (英国号码优先使用Submail)
     * +81=3 (日本号码优先使用润信)
     * +86=4 (中国号码优先使用牛信)
     */
    private volatile Map<String, Integer> prefixChannelMap = new HashMap<>();

    /**
     * 通道支持的国家/地区前缀配置
     * 每个通道支持的国家/地区前缀列表
     * 例如：
     * 1=+1,+44,+61 (Submail支持美国、英国、澳大利亚等)
     * 2=+1,+44,+33 (拉力支持美国、英国、法国等)
     * 3=+81,+82,+86 (润信支持日本、韩国、中国等)
     * 4=+86 (牛信仅支持中国)
     * 5=+86 (振亿仅支持中国)
     */
    private volatile Map<Integer, Set<String>> channelSupportedPrefixesMap = new HashMap<>();

    @Value("${SMS_PREFIX_CHANNEL_MAP:{}}")
    private String prefixChannelMapStr;

    /**
     * 通道支持的国家/地区前缀配置字符串
     */
    @Value("${SMS_CHANNEL_SUPPORTED_PREFIXES:{}}")
    private String channelSupportedPrefixesStr;

    /**
     * 初始化配置
     */
    @PostConstruct
    public void init() {
        updatePrefixChannelMap(prefixChannelMapStr);
        updateChannelSupportedPrefixes(channelSupportedPrefixesStr);
        logChannelStatus();
    }

    /**
     * 记录渠道状态
     */
    private void logChannelStatus() {
        log.info("短信渠道状态 - Submail: {}, 拉力: {}, 润信: {}, 牛信: {}, 振亿: {}, 默认渠道: {}",
                submailEnabled ? "启用" : "禁用",
                laliEnabled ? "启用" : "禁用",
                runxinEnabled ? "启用" : "禁用",
                nxcloudEnabled ? "启用" : "禁用",
                zhenyiEnabled ? "启用" : "禁用",
                getChannelName(defaultChannel));

        // 记录通道支持的国家/地区前缀
        for (Map.Entry<Integer, Set<String>> entry : channelSupportedPrefixesMap.entrySet()) {
            log.info("通道 {} ({}) 支持的国家/地区前缀: {}",
                    entry.getKey(),
                    getChannelName(entry.getKey()),
                    String.join(", ", entry.getValue()));
        }
    }

    /**
     * 解析并更新前缀通道映射
     */
    private void updatePrefixChannelMap(String mapStr) {
        Map<String, Integer> newMap = new HashMap<>();

        try {
            if (mapStr != null && !mapStr.isEmpty() && !mapStr.equals("{}")) {
                // 解析格式如 "+1=2,+44=1,+81=3"
                String[] pairs = mapStr.split(",");
                for (String pair : pairs) {
                    String[] keyValue = pair.trim().split("=");
                    if (keyValue.length == 2) {
                        try {
                            String prefix = keyValue[0].trim();
                            Integer channel = Integer.parseInt(keyValue[1].trim());
                            if (isValidChannel(channel)) {
                                newMap.put(prefix, channel);
                            } else {
                                log.warn("无效的通道ID: {}, 有效范围: 1-5", channel);
                            }
                        } catch (NumberFormatException e) {
                            log.warn("无效的通道ID格式: {}", pair);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("解析SMS_PREFIX_CHANNEL_MAP失败: {}", mapStr, e);
        }

        // 原子性更新映射
        this.prefixChannelMap = newMap;
        log.info("已更新前缀通道映射: {}", newMap);
    }

    /**
     * 解析并更新通道支持的国家/地区前缀
     */
    private void updateChannelSupportedPrefixes(String mapStr) {
        Map<Integer, Set<String>> newMap = new HashMap<>();

        try {
            if (mapStr != null && !mapStr.isEmpty() && !mapStr.equals("{}")) {
                // 解析格式如 "1=+1,+44,+61;2=+1,+44,+33;3=+81,+82,+86;4=+86"
                String[] channelEntries = mapStr.split(";");
                for (String channelEntry : channelEntries) {
                    String[] keyValue = channelEntry.trim().split("=");
                    if (keyValue.length == 2) {
                        try {
                            Integer channelId = Integer.parseInt(keyValue[0].trim());
                            if (isValidChannel(channelId)) {
                                String[] prefixes = keyValue[1].trim().split(",");
                                Set<String> prefixSet = new HashSet<>(Arrays.asList(prefixes));
                                newMap.put(channelId, prefixSet);
                            } else {
                                log.warn("无效的通道ID: {}, 有效范围: 1-5", channelId);
                            }
                        } catch (NumberFormatException e) {
                            log.warn("无效的通道ID格式: {}", channelEntry);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("解析SMS_CHANNEL_SUPPORTED_PREFIXES失败: {}", mapStr, e);
        }

        // 原子性更新映射
        this.channelSupportedPrefixesMap = newMap;
        log.info("已更新通道支持的国家/地区前缀: {}", newMap);
    }

    /**
     * 验证通道ID是否有效
     * @param channelId 通道ID
     * @return 是否有效
     */
    private boolean isValidChannel(int channelId) {
        return channelId >= 1 && channelId <= 5;
    }

    /**
     * 获取通道名称
     * @param channelId 通道ID
     * @return 通道名称
     */
    public String getChannelName(int channelId) {
        switch (channelId) {
            case 1: return "Submail";
            case 2: return "拉力";
            case 3: return "润信";
            case 4: return "牛信";
            case 5: return "振亿";
            default: return "未知渠道";
        }
    }

    /**
     * 检查通道是否可用
     * @param channelId 通道ID
     * @return 是否可用
     */
    public boolean isChannelEnabled(int channelId) {
        switch (channelId) {
            case 1: return submailEnabled;
            case 2: return laliEnabled;
            case 3: return runxinEnabled;
            case 4: return nxcloudEnabled;
            case 5: return zhenyiEnabled;
            default: return false;
        }
    }

    /**
     * 获取所有可用的通道ID列表
     * @return 可用通道ID列表
     */
    public java.util.List<Integer> getAvailableChannels() {
        java.util.List<Integer> channels = new java.util.ArrayList<>();
        if (submailEnabled) channels.add(1);
        if (laliEnabled) channels.add(2);
        if (runxinEnabled) channels.add(3);
        if (nxcloudEnabled) channels.add(4);
        if (zhenyiEnabled) channels.add(5);
        return channels;
    }

    // Getter 方法
    public boolean isSubmailEnabled() {
        return submailEnabled;
    }

    public boolean isLaliEnabled() {
        return laliEnabled;
    }

    public boolean isRunxinEnabled() {
        return runxinEnabled;
    }

    public boolean isNxcloudEnabled() {
        return nxcloudEnabled;
    }

    public boolean isZhenyiEnabled() {
        return zhenyiEnabled;
    }

    public int getDefaultChannel() {
        return defaultChannel;
    }

    public int getSwitchWindowMinutes() {
        return switchWindowMinutes;
    }

    public Map<String, Integer> getPrefixChannelMap() {
        return prefixChannelMap;
    }

    /**
     * 根据号码前缀获取优先通道
     * @param prefix 号码前缀，例如 +1, +44, +81, +86
     * @return 优先通道ID (1: Submail, 2: 拉力, 3: 润信, 4: 牛信)，如果没有配置则返回null
     */
    public Integer getPreferredChannelForPrefix(String prefix) {
        Integer preferredChannel = prefixChannelMap.get(prefix);

        // 检查优先通道是否支持该国家/地区前缀
        if (preferredChannel != null && !isChannelSupportPrefix(preferredChannel, prefix)) {
            log.warn("优先通道 {} ({}) 不支持国家/地区前缀 {}, 将返回null",
                    preferredChannel, getChannelName(preferredChannel), prefix);
            return null;
        }

        return preferredChannel;
    }

    /**
     * 检查通道是否支持指定的国家/地区前缀
     * @param channelId 通道ID
     * @param prefix 国家/地区前缀
     * @return 是否支持
     */
    public boolean isChannelSupportPrefix(int channelId, String prefix) {
        Set<String> supportedPrefixes = channelSupportedPrefixesMap.get(channelId);
        if (supportedPrefixes == null || supportedPrefixes.isEmpty()) {
            // 如果没有配置支持的前缀，默认支持所有前缀
            return true;
        }
        return supportedPrefixes.contains(prefix);
    }

    /**
     * 获取支持指定国家/地区前缀的所有可用通道
     * @param prefix 国家/地区前缀
     * @return 支持该前缀的可用通道ID列表
     */
    public java.util.List<Integer> getSupportedChannelsForPrefix(String prefix) {
        java.util.List<Integer> supportedChannels = new java.util.ArrayList<>();
        for (Integer channelId : getAvailableChannels()) {
            if (isChannelSupportPrefix(channelId, prefix)) {
                supportedChannels.add(channelId);
            }
        }
        return supportedChannels;
    }

    @ApolloConfigChangeListener
    public void onChange(ConfigChangeEvent changeEvent) {
        if (changeEvent.isChanged("SMS_PREFIX_CHANNEL_MAP")) {
            String newValue = changeEvent.getChange("SMS_PREFIX_CHANNEL_MAP").getNewValue();
            updatePrefixChannelMap(newValue);
        }
        if (changeEvent.isChanged("SMS_CHANNEL_SUPPORTED_PREFIXES")) {
            String newValue = changeEvent.getChange("SMS_CHANNEL_SUPPORTED_PREFIXES").getNewValue();
            updateChannelSupportedPrefixes(newValue);
        }
        if (changeEvent.isChanged("SMS_SUBMAIL_ENABLED")) {
            submailEnabled = Boolean.parseBoolean(changeEvent.getChange("SMS_SUBMAIL_ENABLED").getNewValue());
            log.info("Submail渠道状态已更新为: {}", submailEnabled);
        }
        if (changeEvent.isChanged("SMS_LALI_ENABLED")) {
            laliEnabled = Boolean.parseBoolean(changeEvent.getChange("SMS_LALI_ENABLED").getNewValue());
            log.info("拉力渠道状态已更新为: {}", laliEnabled);
        }
        if (changeEvent.isChanged("SMS_RUNXIN_ENABLED")) {
            runxinEnabled = Boolean.parseBoolean(changeEvent.getChange("SMS_RUNXIN_ENABLED").getNewValue());
            log.info("润信渠道状态已更新为: {}", runxinEnabled);
        }
        if (changeEvent.isChanged("SMS_NXCLOUD_ENABLED")) {
            nxcloudEnabled = Boolean.parseBoolean(changeEvent.getChange("SMS_NXCLOUD_ENABLED").getNewValue());
            log.info("牛信渠道状态已更新为: {}", nxcloudEnabled);
        }
        if (changeEvent.isChanged("SMS_DEFAULT_CHANNEL")) {
            int newChannel = Integer.parseInt(changeEvent.getChange("SMS_DEFAULT_CHANNEL").getNewValue());
            if (isValidChannel(newChannel)) {
                defaultChannel = newChannel;
                log.info("默认短信渠道已更新为: {} ({})", defaultChannel, getChannelName(defaultChannel));
            } else {
                log.warn("无效的默认渠道ID: {}, 有效范围: 1-4", newChannel);
            }
        }
        if (changeEvent.isChanged("SMS_SWITCH_WINDOW_MINUTES")) {
            switchWindowMinutes = Integer.parseInt(changeEvent.getChange("SMS_SWITCH_WINDOW_MINUTES").getNewValue());
            log.info("渠道切换时间窗口已更新为: {}分钟", switchWindowMinutes);
        }

        // 配置变更后重新记录状态
        logChannelStatus();
    }
}
