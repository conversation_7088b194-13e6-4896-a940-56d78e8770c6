package com.media.mail.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.media.mail.entity.SmsSendStats;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

/**
 * 短信发送统计Mapper接口
 */
@Mapper
public interface SmsSendStatsMapper extends BaseMapper<SmsSendStats> {

    /**
     * 查询指定时间段内的发送成功数量
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 发送成功数量
     */
    @Select("SELECT COUNT(*) FROM sms_send_stats WHERE status = 'SUCCESS' AND send_time BETWEEN #{startTime} AND #{endTime} AND deleted = 0")
    int countSuccessInTimeRange(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 查询指定时间段内的发送失败数量
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 发送失败数量
     */
    @Select("SELECT COUNT(*) FROM sms_send_stats WHERE status = 'FAILED' AND send_time BETWEEN #{startTime} AND #{endTime} AND deleted = 0")
    int countFailureInTimeRange(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 查询指定手机号的发送记录
     * @param phoneNumber 手机号
     * @param countryCode 国家/地区代码
     * @param limit 限制数量
     * @return 发送记录列表
     */
    @Select("SELECT * FROM sms_send_stats WHERE recipient = #{phoneNumber} AND country_code = #{countryCode} AND deleted = 0 ORDER BY send_time DESC LIMIT #{limit}")
    List<SmsSendStats> findByPhoneNumber(@Param("phoneNumber") String phoneNumber, @Param("countryCode") String countryCode, @Param("limit") int limit);

    /**
     * 查询指定时间段内某个手机号的失败次数
     * @param phoneNumber 手机号
     * @param countryCode 国家/地区代码
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 失败次数
     */
    @Select("SELECT COUNT(*) FROM sms_send_stats WHERE recipient = #{phoneNumber} AND country_code = #{countryCode} AND status = 'FAILED' AND send_time BETWEEN #{startTime} AND #{endTime} AND deleted = 0")
    int countFailureByPhoneInTimeRange(@Param("phoneNumber") String phoneNumber, @Param("countryCode") String countryCode, @Param("startTime") Date startTime, @Param("endTime") Date endTime);
}
