package com.media.mail.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.media.mail.entity.EmailSendStats;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

/**
 * 邮件发送统计信息Mapper接口
 */
@Mapper
public interface EmailSendStatsMapper extends BaseMapper<EmailSendStats> {

    /**
     * 统计指定时间段内的邮件发送成功率
     * @param provider 邮件提供商
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 成功率（百分比）
     */
    @Select("SELECT ROUND(SUM(CASE WHEN status = 'SUCCESS' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) " +
            "FROM email_send_stats " +
            "WHERE provider = #{provider} AND send_time BETWEEN #{startTime} AND #{endTime} AND deleted = 0")
    Double getSuccessRate(@Param("provider") String provider,
                          @Param("startTime") Date startTime,
                          @Param("endTime") Date endTime);

    /**
     * 统计指定时间段内的邮件发送数量
     * @param provider 邮件提供商
     * @param status 发送状态
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 发送数量
     */
    @Select("SELECT COUNT(*) FROM email_send_stats " +
            "WHERE provider = #{provider} AND status = #{status} " +
            "AND send_time BETWEEN #{startTime} AND #{endTime} AND deleted = 0")
    Integer countByProviderAndStatus(@Param("provider") String provider,
                                    @Param("status") String status,
                                    @Param("startTime") Date startTime,
                                    @Param("endTime") Date endTime);

    /**
     * 获取指定接收者的邮件发送历史
     * @param recipient 接收者邮箱
     * @param limit 限制数量
     * @return 邮件发送历史列表
     */
    @Select("SELECT * FROM email_send_stats " +
            "WHERE recipient = #{recipient} AND deleted = 0 " +
            "ORDER BY send_time DESC LIMIT #{limit}")
    List<EmailSendStats> findByRecipient(@Param("recipient") String recipient, @Param("limit") Integer limit);

    /**
     * 获取平均处理时间
     * @param provider 邮件提供商
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 平均处理时间（毫秒）
     */
    @Select("SELECT AVG(processing_time) FROM email_send_stats " +
            "WHERE provider = #{provider} AND status = 'SUCCESS' " +
            "AND send_time BETWEEN #{startTime} AND #{endTime} AND deleted = 0")
    Double getAverageProcessingTime(@Param("provider") String provider,
                                   @Param("startTime") Date startTime,
                                   @Param("endTime") Date endTime);
}
