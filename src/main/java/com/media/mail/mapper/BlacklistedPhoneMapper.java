package com.media.mail.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.media.mail.entity.BlacklistedPhone;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 黑名单手机号Mapper接口
 */
@Mapper
public interface BlacklistedPhoneMapper extends BaseMapper<BlacklistedPhone> {

    /**
     * 根据手机号查询黑名单记录
     * @param phoneNumber 手机号
     * @param countryCode 国家/地区代码
     * @return 黑名单记录
     */
    @Select("SELECT * FROM blacklisted_phone WHERE phone_number = #{phoneNumber} AND country_code = #{countryCode} AND deleted = 0")
    BlacklistedPhone findByPhoneNumber(@Param("phoneNumber") String phoneNumber, @Param("countryCode") String countryCode);

    /**
     * 查询所有未过期的黑名单手机号
     * @return 未过期的黑名单手机号列表
     */
    @Select("SELECT * FROM blacklisted_phone WHERE (permanent = 1 OR expires_at > NOW()) AND deleted = 0")
    List<BlacklistedPhone> findAllActive();

    /**
     * 增加失败计数
     * @param id 黑名单记录ID
     * @return 影响的行数
     */
    @Update("UPDATE blacklisted_phone SET failure_count = failure_count + 1 WHERE id = #{id}")
    int incrementFailureCount(@Param("id") Long id);

    /**
     * 清理过期的黑名单记录
     * @return 影响的行数
     */
    @Update("UPDATE blacklisted_phone SET deleted = 1 WHERE permanent = 0 AND expires_at < NOW() AND deleted = 0")
    int cleanExpiredBlacklist();
}
