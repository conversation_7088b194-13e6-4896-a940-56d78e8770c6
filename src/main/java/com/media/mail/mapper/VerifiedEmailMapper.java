package com.media.mail.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.media.mail.entity.VerifiedEmail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Date;

/**
 * 已验证邮箱Mapper接口
 */
@Mapper
public interface VerifiedEmailMapper extends BaseMapper<VerifiedEmail> {

    /**
     * 根据邮箱地址查询验证记录
     * @param email 邮箱地址
     * @return 验证记录
     */
    @Select("SELECT * FROM verified_email WHERE email = #{email} AND deleted = 0")
    VerifiedEmail findByEmail(@Param("email") String email);

    /**
     * 根据域名查询验证记录
     * @param domain 域名
     * @return 验证记录
     */
    @Select("SELECT * FROM verified_email WHERE domain = #{domain} AND deleted = 0")
    List<VerifiedEmail> findByDomain(@Param("domain") String domain);

    /**
     * 查询所有未过期的已验证邮箱
     * @return 未过期的已验证邮箱列表
     */
    @Select("SELECT * FROM verified_email WHERE (expires_at IS NULL OR expires_at > NOW()) AND deleted = 0")
    List<VerifiedEmail> findAllActive();

    /**
     * 更新验证次数和最后验证时间
     * @param id 记录ID
     * @param lastVerifiedAt 最后验证时间
     * @return 影响的行数
     */
    @Update("UPDATE verified_email SET verify_count = verify_count + 1, last_verified_at = #{lastVerifiedAt} WHERE id = #{id}")
    int incrementVerifyCount(@Param("id") Long id, @Param("lastVerifiedAt") Date lastVerifiedAt);

    /**
     * 清理过期的验证记录
     * @return 影响的行数
     */
    @Update("UPDATE verified_email SET deleted = 1 WHERE expires_at < NOW() AND deleted = 0")
    int cleanExpiredEmails();

    /**
     * 检查域名是否已验证
     * @param domain 域名
     * @return 是否已验证
     */
    @Select("SELECT COUNT(*) FROM verified_email WHERE domain = #{domain} AND (expires_at IS NULL OR expires_at > NOW()) AND deleted = 0")
    int countByDomain(@Param("domain") String domain);
}
