package com.media.mail.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.media.mail.entity.BlacklistedEmail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 黑名单邮箱Mapper接口
 */
@Mapper
public interface BlacklistedEmailMapper extends BaseMapper<BlacklistedEmail> {

    /**
     * 根据邮箱地址查询黑名单记录
     * @param email 邮箱地址
     * @return 黑名单记录
     */
    @Select("SELECT * FROM blacklisted_email WHERE email = #{email} AND deleted = 0")
    BlacklistedEmail findByEmail(@Param("email") String email);

    /**
     * 查询所有未过期的黑名单邮箱
     * @return 未过期的黑名单邮箱列表
     */
    @Select("SELECT * FROM blacklisted_email WHERE (permanent = 1 OR expires_at > NOW()) AND deleted = 0")
    List<BlacklistedEmail> findAllActive();

    /**
     * 增加退信计数
     * @param id 黑名单记录ID
     * @return 影响的行数
     */
    @Update("UPDATE blacklisted_email SET bounce_count = bounce_count + 1 WHERE id = #{id}")
    int incrementBounceCount(@Param("id") Long id);

    /**
     * 清理过期的黑名单记录
     * @return 影响的行数
     */
    @Update("UPDATE blacklisted_email SET deleted = 1 WHERE permanent = 0 AND expires_at < NOW() AND deleted = 0")
    int cleanExpiredBlacklist();
}
