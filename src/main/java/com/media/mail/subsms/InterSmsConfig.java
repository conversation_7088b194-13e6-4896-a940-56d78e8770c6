package com.media.mail.subsms;


import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Data
@Configuration
public class InterSmsConfig {

    @Value("${SUBSMS_CN_APPID:}")
    private String cnAppid;

    @Value("${SUBSMS_CN_APPKEY:}")
    private String cnAppkey;

    @Value("${SUBSMS_INTER_APPID:}")
    private String interAppid;

    @Value("${SUBSMS_INTER_APPKEY:}")
    private String interAppkey;
}




