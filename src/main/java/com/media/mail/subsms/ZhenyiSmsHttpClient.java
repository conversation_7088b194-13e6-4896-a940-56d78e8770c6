package com.media.mail.subsms;

import com.alibaba.fastjson.JSONObject;
import com.media.mail.constant.MailConstant;
import com.media.mail.util.HttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;

import java.util.*;

/**
 * 振亿短信HTTP客户端
 * 实现振亿短信发送接口，支持不同内容批量发送
 */
@Slf4j
@Component
public class ZhenyiSmsHttpClient {

    @Value("${ZHENYI_SMS_ACCOUNT:}")
    private String account;

    @Value("${ZHENYI_SMS_PASSWORD:}")
    private String password;

    @Value("${ZHENYI_SMS_URL:http://域名:8080/sms/v2/send-different}")
    private String apiUrl;

    private static final int MAX_PHONES_PER_BATCH = 1000; // 最大1000个号码

    /**
     * 发送单条短信
     * @param phone 手机号码
     * @param message 短信内容
     * @return 是否发送成功
     */
    public boolean sendSingleSms(String phone, String message) {
        List<String> phones = Arrays.asList(phone);
        List<String> messages = Arrays.asList(message);
        return sendBatchSmsWithDifferentContent(phones, messages);
    }

    /**
     * 批量发送相同内容短信
     * @param phones 手机号码列表
     * @param message 短信内容
     * @return 是否发送成功
     */
    public boolean sendBatchSms(List<String> phones, String message) {
        if (phones == null || phones.isEmpty()) {
            log.warn("振亿短信：手机号码列表为空");
            return false;
        }

        if (message == null || message.trim().isEmpty()) {
            log.warn("振亿短信：短信内容为空");
            return false;
        }

        if (phones.size() > MAX_PHONES_PER_BATCH) {
            log.warn("振亿短信：号码数量超过限制，最大支持{}个号码", MAX_PHONES_PER_BATCH);
            return false;
        }

        // 为每个手机号创建相同的消息内容
        List<String> messages = new ArrayList<>();
        for (int i = 0; i < phones.size(); i++) {
            messages.add(message);
        }

        return sendBatchSmsWithDifferentContent(phones, messages);
    }

    /**
     * 批量发送不同内容短信
     * @param phones 手机号码列表
     * @param messages 短信内容列表（与手机号一一对应）
     * @return 是否发送成功
     */
    public boolean sendBatchSmsWithDifferentContent(List<String> phones, List<String> messages) {
        if (phones == null || phones.isEmpty()) {
            log.warn("振亿短信：手机号码列表为空");
            return false;
        }

        if (messages == null || messages.isEmpty()) {
            log.warn("振亿短信：短信内容列表为空");
            return false;
        }

        if (phones.size() != messages.size()) {
            log.warn("振亿短信：手机号数量({})与消息数量({})不匹配", phones.size(), messages.size());
            return false;
        }

        if (phones.size() > MAX_PHONES_PER_BATCH) {
            log.warn("振亿短信：号码数量超过限制，最大支持{}个号码", MAX_PHONES_PER_BATCH);
            return false;
        }

        try {
            // 生成事务ID
            String transactionId = generateTransactionId();

            // 计算授权密码
            String authPassword = calculateAuthPassword(transactionId);

            // 构建请求体
            JSONObject requestBody = new JSONObject();
            requestBody.put("account", account);
            requestBody.put("transactionId", transactionId);
            requestBody.put("password", authPassword);
            // dTime 可选，为空则立即发送
            // requestBody.put("dTime", "");

            // 构建短信列表
            List<JSONObject> smsList = new ArrayList<>();
            for (int i = 0; i < phones.size(); i++) {
                JSONObject smsItem = new JSONObject();
                smsItem.put("mobile", phones.get(i));
                smsItem.put("content", messages.get(i));
                smsItem.put("uuid", generateUuid());
                // ext 可选，这里设为null
                smsItem.put("ext", null);
                smsList.add(smsItem);
            }
            requestBody.put("list", smsList);

            log.info("振亿短信发送请求，事务ID: {}, 号码数量: {}", transactionId, phones.size());

            // 发送HTTP请求
            String response = HttpUtils.postJson(apiUrl, requestBody.toJSONString());

            log.info("振亿短信发送响应，事务ID: {}, 响应: {}", transactionId, response);

            // 解析响应结果
            return parseResponse(response, transactionId);

        } catch (Exception e) {
            log.error("振亿短信发送失败", e);
            return false;
        }
    }

    /**
     * 使用模板发送短信
     * @param phone 手机号码
     * @param templateVars 模板变量
     * @param templateType 模板类型（中文/英文）
     * @return 是否发送成功
     */
    public boolean sendTemplateSms(String phone, Map<String, String> templateVars, String templateType) {
        // 根据模板类型和变量构建短信内容
        String message = buildMessageFromTemplate(templateVars, templateType);

        if (message == null || message.trim().isEmpty()) {
            log.warn("振亿短信：模板构建失败");
            return false;
        }

        return sendSingleSms(phone, message);
    }

    /**
     * 批量模板短信发送
     * @param phones 手机号码列表
     * @param templateVars 模板变量
     * @param templateType 模板类型
     * @return 是否发送成功
     */
    public boolean sendBatchTemplateSms(List<String> phones, Map<String, String> templateVars, String templateType) {
        String message = buildMessageFromTemplate(templateVars, templateType);

        if (message == null || message.trim().isEmpty()) {
            log.warn("振亿短信：模板构建失败");
            return false;
        }

        return sendBatchSms(phones, message);
    }

    /**
     * 生成事务ID
     * @return 36位事务ID
     */
    private String generateTransactionId() {
        return UUID.randomUUID().toString();
    }

    /**
     * 生成UUID
     * @return 32位UUID
     */
    private String generateUuid() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 计算授权密码
     * 算法：MD5(account + password + transactionId)
     * @param transactionId 事务ID
     * @return 32位MD5小写字符串
     */
    private String calculateAuthPassword(String transactionId) {
        String signStr = account + password + transactionId;
        return DigestUtils.md5DigestAsHex(signStr.getBytes());
    }

    /**
     * 将JSONObject转换为字符串Map
     * @param jsonObject JSON对象
     * @return 字符串Map
     */
    private Map<String, String> convertToStringMap(JSONObject jsonObject) {
        Map<String, String> stringMap = new HashMap<>();
        for (String key : jsonObject.keySet()) {
            Object value = jsonObject.get(key);
            if (value != null) {
                stringMap.put(key, value.toString());
            }
        }
        return stringMap;
    }

    /**
     * 解析响应结果
     * @param response 响应字符串
     * @param transactionId 事务ID
     * @return 是否成功
     */
    private boolean parseResponse(String response, String transactionId) {
        try {
            if (response == null || response.trim().isEmpty()) {
                log.warn("振亿短信响应为空，事务ID: {}", transactionId);
                return false;
            }

            // 尝试解析为JSON
            JSONObject jsonResponse = JSONObject.parseObject(response);

            // 检查success字段
            Boolean success = jsonResponse.getBoolean("success");
            String responseTransactionId = jsonResponse.getString("transactionId");

            if (Boolean.TRUE.equals(success)) {
                log.info("振亿短信发送成功，事务ID: {}", transactionId);
                return true;
            } else {
                // 检查失败列表
                com.alibaba.fastjson.JSONArray failListArray = jsonResponse.getJSONArray("failList");
                if (failListArray != null && !failListArray.isEmpty()) {
                    log.warn("振亿短信发送部分失败，事务ID: {}, 失败数量: {}", transactionId, failListArray.size());
                    for (int i = 0; i < failListArray.size(); i++) {
                        JSONObject failItem = failListArray.getJSONObject(i);
                        String mobile = failItem.getString("mobile");
                        String errorCode = failItem.getString("errorCode");
                        String errorDesc = failItem.getString("errorDesc");
                        String uuid = failItem.getString("uuid");
                        log.warn("振亿短信发送失败 - 手机号: {}, 错误码: {}, 错误信息: {}, UUID: {}",
                                mobile, errorCode, errorDesc, uuid);
                    }
                } else {
                    log.warn("振亿短信发送失败，事务ID: {}", transactionId);
                }
                return false;
            }

        } catch (Exception e) {
            log.error("振亿短信响应解析失败，事务ID: {}, 响应: {}", transactionId, response, e);
            return false;
        }
    }

    /**
     * 根据模板变量构建短信内容
     * @param templateVars 模板变量
     * @param templateType 模板类型
     * @return 短信内容
     */
    private String buildMessageFromTemplate(Map<String, String> templateVars, String templateType) {
        if (templateVars == null || templateVars.isEmpty()) {
            log.warn("振亿短信：模板变量为空");
            return null;
        }

        try {
            // 根据模板类型选择不同的模板
            String message = MailConstant.MESSAGE_TEMPLATE;

            // 替换模板变量
            for (Map.Entry<String, String> entry : templateVars.entrySet()) {
                String placeholder = "${" + entry.getKey() + "}";
                message = message.replace(placeholder, entry.getValue());
            }

            // 检查是否还有未替换的变量
            if (message.contains("${")) {
                log.warn("振亿短信：模板中存在未替换的变量，模板: {}", message);
            }

            return message;

        } catch (Exception e) {
            log.error("振亿短信：模板构建失败", e);
            return null;
        }
    }

    /**
     * 检查配置是否完整
     * @return 配置是否完整
     */
    public boolean isConfigValid() {
        return account != null && !account.trim().isEmpty() &&
               password != null && !password.trim().isEmpty() &&
               apiUrl != null && !apiUrl.trim().isEmpty();
    }

    /**
     * 获取配置状态信息
     * @return 配置状态
     */
    public String getConfigStatus() {
        if (isConfigValid()) {
            return "振亿短信配置完整";
        } else {
            return String.format("振亿短信配置不完整 - account: %s, password: %s, apiUrl: %s",
                    account != null && !account.trim().isEmpty() ? "已配置" : "未配置",
                    password != null && !password.trim().isEmpty() ? "已配置" : "未配置",
                    apiUrl != null && !apiUrl.trim().isEmpty() ? "已配置" : "未配置");
        }
    }
}
