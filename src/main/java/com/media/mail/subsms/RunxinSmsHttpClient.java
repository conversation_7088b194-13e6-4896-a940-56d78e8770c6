package com.media.mail.subsms;

import com.alibaba.fastjson.JSONObject;
import com.media.mail.constant.MailConstant;
import com.media.mail.util.HttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 润信短信HTTP客户端
 * 实现润信短信批量发送接口
 */
@Slf4j
@Component
public class RunxinSmsHttpClient {

    @Value("${RUNXIN_SMS_APPKEY:}")
    private String appKey;

    @Value("${RUNXIN_SMS_APPCODE:}")
    private String appCode;

    @Value("${RUNXIN_SMS_APPSECRET:}")
    private String appSecret;

    @Value("${RUNXIN_SMS_URL:http://47.238.243.213:9090/sms/batch/v2}")
    private String apiUrl;

    private static final int MAX_PHONES_PER_BATCH = 1000; // 最大1000个号码

    /**
     * 发送单条短信
     * @param phone 手机号码
     * @param message 短信内容
     * @return 是否发送成功
     */
    public boolean sendSingleSms(String phone, String message) {
        List<String> phones = Arrays.asList(phone);
        return sendBatchSms(phones, message);
    }

    /**
     * 批量发送短信
     * @param phones 手机号码列表
     * @param message 短信内容
     * @return 是否发送成功
     */
    public boolean sendBatchSms(List<String> phones, String message) {
        if (phones == null || phones.isEmpty()) {
            log.warn("润信短信：手机号码列表为空");
            return false;
        }

        if (message == null || message.trim().isEmpty()) {
            log.warn("润信短信：短信内容为空");
            return false;
        }

        if (phones.size() > MAX_PHONES_PER_BATCH) {
            log.warn("润信短信：号码数量超过限制，最大支持{}个号码", MAX_PHONES_PER_BATCH);
            return false;
        }

        try {
            // 生成唯一ID
            String uid = generateUid();

            // 构建请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("appkey", appKey);
            params.put("appcode", appCode);
            params.put("appsecret", appSecret);
            params.put("uid", uid);

            // 将手机号列表转换为逗号分隔的字符串
            String phoneStr = String.join(",", phones);
            params.put("phone", phoneStr);
            params.put("msg", message);

            log.info("润信短信发送请求，UID: {}, 号码数量: {}, 内容长度: {}",
                    uid, phones.size(), message.length());

            // 发送HTTP请求
            String response = HttpUtils.postWithParamsForString(apiUrl, convertToStringMap(params));

            log.info("润信短信发送响应，UID: {}, 响应: {}", uid, response);

            // 解析响应结果
            return parseResponse(response, uid);

        } catch (Exception e) {
            log.error("润信短信发送失败", e);
            return false;
        }
    }

    /**
     * 使用模板发送短信
     * @param phone 手机号码
     * @param templateVars 模板变量
     * @param templateType 模板类型（中文/英文）
     * @return 是否发送成功
     */
    public boolean sendTemplateSms(String phone, Map<String, String> templateVars, String templateType) {
        // 根据模板类型和变量构建短信内容
        String message = buildMessageFromTemplate(templateVars, templateType);

        if (message == null || message.trim().isEmpty()) {
            log.warn("润信短信：模板构建失败");
            return false;
        }

        return sendSingleSms(phone, message);
    }

    /**
     * 批量模板短信发送
     * @param phones 手机号码列表
     * @param templateVars 模板变量
     * @param templateType 模板类型
     * @return 是否发送成功
     */
    public boolean sendBatchTemplateSms(List<String> phones, Map<String, String> templateVars, String templateType) {
        String message = buildMessageFromTemplate(templateVars, templateType);

        if (message == null || message.trim().isEmpty()) {
            log.warn("润信短信：模板构建失败");
            return false;
        }

        return sendBatchSms(phones, message);
    }

    /**
     * 生成唯一ID
     * @return 32位字符串ID
     */
    private String generateUid() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 将参数Map转换为字符串Map
     * @param params 原始参数Map
     * @return 字符串Map
     */
    private Map<String, String> convertToStringMap(Map<String, Object> params) {
        Map<String, String> stringMap = new HashMap<>();
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            stringMap.put(entry.getKey(), String.valueOf(entry.getValue()));
        }
        return stringMap;
    }

    /**
     * 解析响应结果
     * @param response 响应字符串
     * @param uid 请求UID
     * @return 是否成功
     */
    private boolean parseResponse(String response, String uid) {
        try {
            if (response == null || response.trim().isEmpty()) {
                log.warn("润信短信响应为空，UID: {}", uid);
                return false;
            }

            // 尝试解析为JSON
            JSONObject jsonResponse = JSONObject.parseObject(response);

            // 检查是否有错误码或状态码
            if (jsonResponse.containsKey("code")) {
                String code = jsonResponse.getString("code");
                String message = jsonResponse.getString("message");

                if ("200".equals(code) || "00000".equals(code) || "success".equalsIgnoreCase(code)) {
                    log.info("润信短信发送成功，UID: {}, 响应: {}", uid, message);
                    return true;
                } else {
                    log.warn("润信短信发送失败，UID: {}, 错误码: {}, 错误信息: {}", uid, code, message);
                    return false;
                }
            }

            // 如果没有明确的错误码，检查常见的成功标识
            String responseStr = response.toLowerCase();
            if (responseStr.contains("success") || responseStr.contains("ok") || responseStr.contains("\"code\":\"200\"")) {
                log.info("润信短信发送成功，UID: {}", uid);
                return true;
            } else {
                log.warn("润信短信发送可能失败，UID: {}, 响应: {}", uid, response);
                return false;
            }

        } catch (Exception e) {
            log.error("润信短信响应解析失败，UID: {}, 响应: {}", uid, response, e);
            return false;
        }
    }

    /**
     * 根据模板变量构建短信内容
     * @param templateVars 模板变量
     * @param templateType 模板类型
     * @return 短信内容
     */
    private String buildMessageFromTemplate(Map<String, String> templateVars, String templateType) {
        if (templateVars == null || templateVars.isEmpty()) {
            log.warn("润信短信：模板变量为空");
            return null;
        }

        try {
            // 替换模板变量
            String message = MailConstant.MESSAGE_TEMPLATE;
            for (Map.Entry<String, String> entry : templateVars.entrySet()) {
                String placeholder = "${" + entry.getKey() + "}";
                message = message.replace(placeholder, entry.getValue());
            }

            // 检查是否还有未替换的变量
            if (message.contains("${")) {
                log.warn("润信短信：模板中存在未替换的变量，模板: {}", message);
            }

            return message;

        } catch (Exception e) {
            log.error("润信短信：模板构建失败", e);
            return null;
        }
    }

    /**
     * 检查配置是否完整
     * @return 配置是否完整
     */
    public boolean isConfigValid() {
        return appKey != null && !appKey.trim().isEmpty() &&
               appCode != null && !appCode.trim().isEmpty() &&
               appSecret != null && !appSecret.trim().isEmpty() &&
               apiUrl != null && !apiUrl.trim().isEmpty();
    }

    /**
     * 获取配置状态信息
     * @return 配置状态
     */
    public String getConfigStatus() {
        if (isConfigValid()) {
            return "润信短信配置完整";
        } else {
            return String.format("润信短信配置不完整 - appKey: %s, appCode: %s, appSecret: %s, apiUrl: %s",
                    appKey != null && !appKey.trim().isEmpty() ? "已配置" : "未配置",
                    appCode != null && !appCode.trim().isEmpty() ? "已配置" : "未配置",
                    appSecret != null && !appSecret.trim().isEmpty() ? "已配置" : "未配置",
                    apiUrl != null && !apiUrl.trim().isEmpty() ? "已配置" : "未配置");
        }
    }
}
