package com.media.mail.subsms;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.TreeMap;

@Slf4j
@Component
public class CnSmsHttpClient extends CommonClient {

    @Value("${SMS_CN_TEMPLATE_ID:qQj673}")
    private String cnTemplateId;

    @Value("${SMS_EN_TEMPLATE_ID:VuJVC3}")
    private String enTemplateId;

    @Autowired
    InterSmsConfig interSmsConfig;

    private static final String SEND_URL = "https://api-v4.mysubmail.com/sms/send";
    private static final String XSEND_URL = "https://api-v4.mysubmail.com/sms/xsend";

    public static final String TYPE_MD5 = "md5";
    public static final String SIGN_VERSION = "2";
    /**
     * 直接发送 content
     * @param to
     * @param content
     */
    public void sendByContent(String to, String content) {
        TreeMap<String, String> requestData = new TreeMap<>();
        String appid = interSmsConfig.getCnAppid();
        String appkey = interSmsConfig.getCnAppkey();

        //组合请求数据
        requestData.put("appid", appid);
        requestData.put("to", to);
        requestData.put("timestamp", super.getTimestamp());
        requestData.put("sign_type", TYPE_MD5);
        requestData.put("sign_version", SIGN_VERSION);
        String signStr = appid + appkey + RequestEncoder.formatRequest(requestData) + appid + appkey;
        log.info("生成签名:{}", signStr);
        requestData.put("signature", RequestEncoder.encode(TYPE_MD5, signStr));
        requestData.put("content", content);
        //发送短息服务
        super.httpPost(SEND_URL, requestData);
    }

    /**
     * 使用模板发送短信通知
     * @param to 接收手机号
     * @param vars 模板变量数据
     * @param isEnglish 是否使用英文模板
     * @return 是否发送成功
     */
    public boolean sendSms(String to, Map<String, String> vars, boolean isEnglish) {
        try {
            String templateId = isEnglish ? enTemplateId : cnTemplateId;

            if (templateId == null || templateId.isEmpty()) {
                log.error("模板 ID 不能为空");
                return false;
            }

            // 将变量数据转换为JSON字符串
            JSONObject varsJson = new JSONObject();
            if (vars != null && !vars.isEmpty()) {
                for (Map.Entry<String, String> entry : vars.entrySet()) {
                    varsJson.put(entry.getKey(), entry.getValue());
                }
            }

            TreeMap<String, String> requestData = new TreeMap<>();
            String appid = interSmsConfig.getCnAppid();
            String appkey = interSmsConfig.getCnAppkey();

            //组合请求数据
            requestData.put("appid", appid);
            requestData.put("to", to);
            requestData.put("project", templateId);
            requestData.put("timestamp", super.getTimestamp());
            requestData.put("sign_type", TYPE_MD5);
            requestData.put("sign_version", SIGN_VERSION);

            String signStr = appid + appkey + RequestEncoder.formatRequest(requestData) + appid + appkey;
            log.info("生成签名:{}", signStr);
            requestData.put("signature", RequestEncoder.encode(TYPE_MD5, signStr));

            //模板参数
            requestData.put("vars", varsJson.toJSONString());

            //发送短息服务
            super.httpPost(XSEND_URL, requestData);
            log.info("模板短信发送请求已发出，模板ID: {}", templateId);

            return true;
        } catch (Exception e) {
            log.error("发送模板短信失败", e);
            return false;
        }
    }

    /**
     * 使用中文模板发送短信
     * @param to 接收手机号
     * @param vars 模板变量数据
     * @return 是否发送成功
     */
    public boolean sendChineseSms(String to, Map<String, String> vars) {
        return sendSms(to, vars, false);
    }

    /**
     * 使用英文模板发送短信
     * @param to 接收手机号
     * @param vars 模板变量数据
     * @return 是否发送成功
     */
    public boolean sendEnglishSms(String to, Map<String, String> vars) {
        return sendSms(to, vars, true);
    }
}
