package com.media.mail.subsms;

import com.alibaba.fastjson.JSONObject;
import com.media.mail.constant.MailConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.util.*;

/**
 * 牛信短信HTTP客户端
 * 实现牛信短信发送接口和签名机制
 */
@Slf4j
@Component
public class NxcloudSmsHttpClient {

    @Value("${NXCLOUD_SMS_ACCESS_KEY:}")
    private String accessKey;

    @Value("${NXCLOUD_SMS_ACCESS_SECRET:}")
    private String accessSecret;

    @Value("${NXCLOUD_SMS_APP_KEY:}")
    private String appKey;

    @Value("${NXCLOUD_SMS_URL:https://api.nxcloud.com/v1/sms/mt}")
    private String apiUrl;

    private static final String BIZ_TYPE = "3";
    private static final String ACTION = "mtsend";
    private static final int MAX_PHONES_PER_BATCH = 5; // 验证码通道最大5个号码
    private static final int MAX_CONTENT_LENGTH = 1000; // 最大内容长度

    private final HttpClient httpClient;

    public NxcloudSmsHttpClient() {
        this.httpClient = HttpClient.newBuilder()
                .connectTimeout(Duration.ofSeconds(10))
                .build();
    }

    /**
     * 发送单条短信
     * @param phone 手机号码（国码+手机号，如：8615088888888）
     * @param content 短信内容
     * @return 是否发送成功
     */
    public boolean sendSingleSms(String phone, String content) {
        return sendBatchSms(Arrays.asList(phone), content);
    }

    /**
     * 批量发送短信
     * @param phones 手机号码列表
     * @param content 短信内容
     * @return 是否发送成功
     */
    public boolean sendBatchSms(List<String> phones, String content) {
        if (phones == null || phones.isEmpty()) {
            log.warn("牛信短信：手机号码列表为空");
            return false;
        }

        if (content == null || content.trim().isEmpty()) {
            log.warn("牛信短信：短信内容为空");
            return false;
        }

        if (content.length() > MAX_CONTENT_LENGTH) {
            log.warn("牛信短信：内容超过最大长度{}字符", MAX_CONTENT_LENGTH);
            return false;
        }

        if (phones.size() > MAX_PHONES_PER_BATCH) {
            log.warn("牛信短信：号码数量超过限制，最大支持{}个号码", MAX_PHONES_PER_BATCH);
            return false;
        }

        try {
            // 生成时间戳
            String timestamp = String.valueOf(System.currentTimeMillis());

            // 构建请求体
            JSONObject requestBody = new JSONObject();
            requestBody.put("appKey", appKey);
            requestBody.put("phone", String.join(",", phones));
            requestBody.put("content", content);

            String bodyStr = requestBody.toJSONString();

            // 构建请求头
            Map<String, String> headers = new HashMap<>();
            headers.put("accessKey", accessKey);
            headers.put("ts", timestamp);
            headers.put("bizType", BIZ_TYPE);
            headers.put("action", ACTION);

            // 计算签名
            String sign = calculateSign(headers, bodyStr, accessSecret);
            headers.put("sign", sign);

            log.info("牛信短信发送请求，号码数量: {}, 内容长度: {}", phones.size(), content.length());

            // 发送HTTP请求
            HttpRequest.Builder requestBuilder = HttpRequest.newBuilder()
                    .uri(URI.create(apiUrl))
                    .POST(HttpRequest.BodyPublishers.ofString(bodyStr))
                    .header("Content-Type", "application/json");

            // 添加所有头部参数
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                requestBuilder.header(entry.getKey(), entry.getValue());
            }

            HttpRequest request = requestBuilder.build();
            HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());

            log.info("牛信短信发送响应，状态码: {}, 响应: {}", response.statusCode(), response.body());

            // 解析响应结果
            return parseResponse(response.body(), response.statusCode());

        } catch (Exception e) {
            log.error("牛信短信发送失败", e);
            return false;
        }
    }

    /**
     * 使用模板发送短信
     * @param phone 手机号码
     * @param templateVars 模板变量
     * @param templateType 模板类型（中文/英文）
     * @return 是否发送成功
     */
    public boolean sendTemplateSms(String phone, Map<String, String> templateVars, String templateType) {
        // 根据模板类型和变量构建短信内容
        String content = buildMessageFromTemplate(templateVars, templateType);

        if (content == null || content.trim().isEmpty()) {
            log.warn("牛信短信：模板构建失败");
            return false;
        }

        return sendSingleSms(phone, content);
    }

    /**
     * 批量模板短信发送
     * @param phones 手机号码列表
     * @param templateVars 模板变量
     * @param templateType 模板类型
     * @return 是否发送成功
     */
    public boolean sendBatchTemplateSms(List<String> phones, Map<String, String> templateVars, String templateType) {
        String content = buildMessageFromTemplate(templateVars, templateType);

        if (content == null || content.trim().isEmpty()) {
            log.warn("牛信短信：模板构建失败");
            return false;
        }

        return sendBatchSms(phones, content);
    }

    /**
     * 计算签名
     * 签名算法：hex(md5(headersStr + bodyStr + accessSecretStr))
     * @param headers 头部参数
     * @param body 请求体JSON字符串
     * @param accessSecret 密钥
     * @return 签名字符串
     */
    private String calculateSign(Map<String, String> headers, String body, String accessSecret) {
        StringBuilder signStr = new StringBuilder();

        // step1: 拼接header参数（按ASCII码升序排列，除了sign以外）
        signStr.append("accessKey=").append(headers.get("accessKey"))
               .append("&action=").append(headers.get("action"))
               .append("&bizType=").append(headers.get("bizType"))
               .append("&ts=").append(headers.get("ts"));

        log.info("牛信签名step1: {}", signStr);

        // step2: 拼接body参数
        if (body != null && !body.trim().isEmpty()) {
            signStr.append("&body=").append(body);
        }

        log.info("牛信签名step2: {}", signStr);

        // step3: 拼接accessSecret
        signStr.append("&accessSecret=").append(accessSecret);

        log.info("牛信签名step3: {}", signStr);

        // step4: MD5算法加密,结果转换成十六进制小写
        String sign = DigestUtils.md5DigestAsHex(signStr.toString().getBytes());

        log.info("牛信签名step4: {}", sign);

        return sign;
    }

    /**
     * 解析响应结果
     * @param responseBody 响应体
     * @param statusCode HTTP状态码
     * @return 是否成功
     */
    private boolean parseResponse(String responseBody, int statusCode) {
        try {
            if (statusCode != 200) {
                log.warn("牛信短信响应状态码异常: {}", statusCode);
                return false;
            }

            if (responseBody == null || responseBody.trim().isEmpty()) {
                log.warn("牛信短信响应为空");
                return false;
            }

            // 尝试解析为JSON
            JSONObject jsonResponse = JSONObject.parseObject(responseBody);

            // 检查错误码
            if (jsonResponse.containsKey("code")) {
                int code = jsonResponse.getIntValue("code");
                String message = jsonResponse.getString("message");

                if (code == 0) {
                    log.info("牛信短信发送成功，消息: {}", message);

                    // 记录消息ID
                    if (jsonResponse.containsKey("data")) {
                        JSONObject data = jsonResponse.getJSONObject("data");
                        if (data.containsKey("messageid")) {
                            String messageId = data.getString("messageid");
                            log.info("牛信短信消息ID: {}", messageId);
                        }
                    }

                    return true;
                } else {
                    log.warn("牛信短信发送失败，错误码: {}, 错误信息: {}", code, message);
                    return false;
                }
            }

            // 如果没有明确的错误码，判断为失败
            log.warn("牛信短信响应格式异常: {}", responseBody);
            return false;

        } catch (Exception e) {
            log.error("牛信短信响应解析失败，响应: {}", responseBody, e);
            return false;
        }
    }

    /**
     * 根据模板变量构建短信内容
     * @param templateVars 模板变量
     * @param templateType 模板类型
     * @return 短信内容
     */
    private String buildMessageFromTemplate(Map<String, String> templateVars, String templateType) {
        if (templateVars == null || templateVars.isEmpty()) {
            log.warn("牛信短信：模板变量为空");
            return null;
        }

        try {
            // 替换模板变量
            String message = MailConstant.MESSAGE_TEMPLATE;
            for (Map.Entry<String, String> entry : templateVars.entrySet()) {
                String placeholder = "${" + entry.getKey() + "}";
                message = message.replace(placeholder, entry.getValue());
            }

            // 检查是否还有未替换的变量
            if (message.contains("${")) {
                log.warn("牛信短信：模板中存在未替换的变量，模板: {}", message);
            }

            return message;

        } catch (Exception e) {
            log.error("牛信短信：模板构建失败", e);
            return null;
        }
    }

    /**
     * 检查配置是否完整
     * @return 配置是否完整
     */
    public boolean isConfigValid() {
        return accessKey != null && !accessKey.trim().isEmpty() &&
               accessSecret != null && !accessSecret.trim().isEmpty() &&
               appKey != null && !appKey.trim().isEmpty() &&
               apiUrl != null && !apiUrl.trim().isEmpty();
    }

    /**
     * 获取配置状态信息
     * @return 配置状态
     */
    public String getConfigStatus() {
        if (isConfigValid()) {
            return "牛信短信配置完整";
        } else {
            return String.format("牛信短信配置不完整 - accessKey: %s, accessSecret: %s, appKey: %s, apiUrl: %s",
                    accessKey != null && !accessKey.trim().isEmpty() ? "已配置" : "未配置",
                    accessSecret != null && !accessSecret.trim().isEmpty() ? "已配置" : "未配置",
                    appKey != null && !appKey.trim().isEmpty() ? "已配置" : "未配置",
                    apiUrl != null && !apiUrl.trim().isEmpty() ? "已配置" : "未配置");
        }
    }
}
