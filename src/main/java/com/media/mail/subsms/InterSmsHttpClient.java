package com.media.mail.subsms;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.media.mail.bean.config.LaLiConfigInfo;
import com.media.mail.util.HttpUtils;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.TreeMap;

@Slf4j
@Component
public class InterSmsHttpClient extends CommonClient{

    @Value("${SMS_CN_TEMPLATE_ID:qQj673}")
    private String cnTemplateId;

    @Value("${SMS_EN_TEMPLATE_ID:VuJVC3}")
    private String enTemplateId;

    @Autowired
    InterSmsConfig interSmsConfig;

    @ApolloJsonValue("${laLi.sms.config.info:{}}")
    private LaLiConfigInfo laLiConfigInfo;

    private static final String URL_CONTENT = "https://api-v4.mysubmail.com/internationalsms/send.json";
    private static final String URL_PROJECT = "https://api-v4.mysubmail.com/internationalsms/xsend.json";

    public static final String TYPE_MD5 = "md5";
    public static final String SIGN_VERSION = "2";

    /**
     * 直接发送 content
     * @param to
     * @param content
     */
    public boolean sendByContent(String to, String content) {
        try {
            TreeMap<String, String> requestData = new TreeMap<>();
            String appid = interSmsConfig.getInterAppid();
            String appkey = interSmsConfig.getInterAppkey();

            //组合请求数据
            requestData.put("appid", appid);
            requestData.put("to", to);
            requestData.put("timestamp", super.getTimestamp());
            requestData.put("sign_type", TYPE_MD5);
            requestData.put("sign_version", SIGN_VERSION);
            String signStr = appid + appkey + RequestEncoder.formatRequest(requestData) + appid + appkey;
            log.info("生成签名:{}", signStr);
            requestData.put("signature", RequestEncoder.encode(TYPE_MD5, signStr));
            requestData.put("content", content);
            //发送短息服务
            super.httpPost(URL_CONTENT, requestData);
            return true;
        }catch (Exception e){
            log.error("sendByContent 失败", e);
            return false;
        }
    }

    /**
     * 使用模板发送短信
     * @param to 接收手机号
     * @param project 模板ID
     * @param vars 模板变量 JSON 字符串
     */
    public boolean sendByProject(String to, String project, String vars) {
        try {
            TreeMap<String, String> requestData = new TreeMap<>();
            String appid = interSmsConfig.getInterAppid();
            String appkey = interSmsConfig.getInterAppkey();
            //组合请求数据
            requestData.put("appid", appid);
            requestData.put("to", to);
            requestData.put("project", project);
            requestData.put("timestamp", super.getTimestamp());
            requestData.put("sign_type", TYPE_MD5);
            requestData.put("sign_version", SIGN_VERSION);
            String signStr = appid + appkey + RequestEncoder.formatRequest(requestData) + appid + appkey;
            log.info("生成签名:{}", signStr);
            requestData.put("signature", RequestEncoder.encode(TYPE_MD5, signStr));
            //模板参数
            requestData.put("vars", vars);
            //发送短息服务
            super.httpPost(URL_PROJECT, requestData);
            return true;
        }catch (Exception e){
            log.error("sendByProject error", e);
            return false;
        }
    }

    /**
     * 使用模板发送短信
     * @param to 接收手机号
     * @param vars 模板变量数据
     * @param isEnglish 是否使用英文模板
     * @return 是否发送成功
     */
    public boolean sendSms(String to, Map<String, String> vars, boolean isEnglish) {
        try {
            String templateId = isEnglish ? enTemplateId : cnTemplateId;

            if (templateId == null || templateId.isEmpty()) {
                log.error("国际短信模板 ID 不能为空");
                return false;
            }

            // 将变量数据转换为JSON字符串
            JSONObject varsJson = new JSONObject();
            if (vars != null && !vars.isEmpty()) {
                for (Map.Entry<String, String> entry : vars.entrySet()) {
                    varsJson.put(entry.getKey(), entry.getValue());
                }
            }

            TreeMap<String, String> requestData = new TreeMap<>();
            String appid = interSmsConfig.getInterAppid();
            String appkey = interSmsConfig.getInterAppkey();

            //组合请求数据
            requestData.put("appid", appid);
            requestData.put("to", to);
            requestData.put("project", templateId);
            requestData.put("timestamp", super.getTimestamp());
            requestData.put("sign_type", TYPE_MD5);
            requestData.put("sign_version", SIGN_VERSION);

            String signStr = appid + appkey + RequestEncoder.formatRequest(requestData) + appid + appkey;
            log.info("生成签名:{}", signStr);
            requestData.put("signature", RequestEncoder.encode(TYPE_MD5, signStr));

            //模板参数
            requestData.put("vars", varsJson.toJSONString());

            //发送短息服务
            super.httpPost(URL_PROJECT, requestData);
            log.info("国际模板短信发送请求已发出，模板ID: {}", templateId);

            return true;
        } catch (Exception e) {
            log.error("发送国际模板短信失败", e);
            return false;
        }
    }

    /**
     * 拉力发送短信
     *
     * @param to   接收手机号
     * @param vars 变量数据
     * @return 是否发送成功
     */
    public boolean sendLaLiSms(String to, Map<String, String> vars) {
        try {
            if (laLiConfigInfo == null) {
                log.info("发送拉力国际短信配置为空");
                return false;
            }
            String spId = laLiConfigInfo.getSpId();
            String password = laLiConfigInfo.getPassword();
            String sendSmsSingleUrl = laLiConfigInfo.getSendSmsSingleUrl();
            Map<String, String> businessContentMap = laLiConfigInfo.getBusinessContentMap();
            if (StringUtils.isEmpty(spId) || StringUtils.isEmpty(password) || StringUtils.isEmpty(sendSmsSingleUrl) || businessContentMap.isEmpty()) {
                log.info("发送拉力国际短信配置信息有误, spId = {}, password = {}, sendSmsSingleUrl = {}, businessContentMap = {}", spId, password, sendSmsSingleUrl, JSON.toJSONString(businessContentMap));
                return false;
            }
            if (vars.isEmpty()) {
                log.info("发送拉力国际短信vars为空");
                return false;
            }
            String businessType = vars.get("businessType");
            if (StringUtils.isEmpty(businessType)) {
                businessType = "default";
            }
            String businessContent = businessContentMap.get(businessType);
            if (StringUtils.isEmpty(businessContent)) {
                log.info("发送拉力国际短信业务内容为空");
                return false;
            }
            Map<String, String> paramMap = Maps.newHashMap();
            paramMap.put("sp_id", spId);
            paramMap.put("password", password);
            char c = to.charAt(0);
            if (c == '+') {
                to = to.substring(1);
            }
            paramMap.put("mobile", to);
            if ("default".equals(businessType)) {
                String code = vars.get("code");
                String time = vars.get("time");
                businessContent = String.format(businessContent, code, time);
            }
            paramMap.put("content", businessContent);
            HttpUtils.postWithParamsForString(sendSmsSingleUrl, paramMap);
            log.info("拉力国际短信发送请求已发出，businessType = {}", businessType);
        } catch (Exception e) {
            log.error("拉力国际短信发送请求发出失败, to = {}. vars = {}, error = {}", to, JSON.toJSONString(vars), ExceptionUtils.getStackTrace(e));
            return false;
        }
        return true;
    }
}
