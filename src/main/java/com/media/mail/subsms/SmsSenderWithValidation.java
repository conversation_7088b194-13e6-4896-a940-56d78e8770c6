package com.media.mail.subsms;

import com.media.mail.metrics.SmsMetrics;
import com.media.mail.service.BlacklistedPhoneService;
import com.media.mail.util.PhoneValidator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 带验证功能的短信发送器
 * 在发送短信前验证手机号的有效性和检查黑名单
 */
@Component
public class SmsSenderWithValidation {

    private static final Logger logger = LoggerFactory.getLogger(SmsSenderWithValidation.class);

    @Autowired
    private CnSmsHttpClient cnSmsHttpClient;

    @Autowired
    private InterSmsHttpClient interSmsHttpClient;

    @Autowired
    private ZhenyiSmsHttpClient zhenyiSmsHttpClient;

    @Autowired
    private PhoneValidator phoneValidator;

    @Autowired
    private BlacklistedPhoneService blacklistedPhoneService;

    @Autowired
    private SmsMetrics smsMetrics;

    /**
     * 发送中文短信，带验证
     * @param to 接收手机号
     * @param countryCode 国家/地区代码
     * @param vars 模板变量数据
     * @return 是否发送成功
     */
    public boolean sendChineseSms(String to, String countryCode, Map<String, String> vars) {
        long startTime = System.currentTimeMillis();

        try {
            // 标准化手机号
            to = phoneValidator.normalizePhoneNumber(to);
            if (countryCode == null || countryCode.isEmpty()) {
                countryCode = "86"; // 默认中国区号
            }

            // 验证手机号
            PhoneValidator.ValidationResult validationResult = phoneValidator.validate(to, countryCode);
            if (!validationResult.isValid()) {
                logger.warn("手机号验证失败: {}-{}, 原因: {}", countryCode, to, validationResult.getMessage());
                smsMetrics.recordValidationFailed(validationResult.getMessage());

                // 记录发送失败
                long processingTime = System.currentTimeMillis() - startTime;
                smsMetrics.recordSmsSent("submail", "chinese", to, countryCode, false,
                        processingTime, "手机号验证失败: " + validationResult.getMessage());

                return false;
            }

            // 检查黑名单
            if (blacklistedPhoneService.isBlacklisted(to, countryCode)) {
                logger.warn("手机号在黑名单中: {}-{}", countryCode, to);
                smsMetrics.recordBlacklisted(countryCode);

                // 记录发送失败
                long processingTime = System.currentTimeMillis() - startTime;
                smsMetrics.recordSmsSent("submail", "chinese", to, countryCode, false,
                        processingTime, "手机号在黑名单中");

                return false;
            }

            // 发送短信
            boolean success = cnSmsHttpClient.sendChineseSms(to, vars);

            // 记录发送结果
            long processingTime = System.currentTimeMillis() - startTime;
            smsMetrics.recordSmsSent("submail", "chinese", to, countryCode, success,
                    processingTime, success ? null : "短信发送失败");

            return success;
        } catch (Exception e) {
            logger.error("发送中文短信失败: {}-{}", countryCode, to, e);

            // 记录发送失败
            long processingTime = System.currentTimeMillis() - startTime;
            smsMetrics.recordSmsSent("submail", "chinese", to, countryCode, false,
                    processingTime, "发送异常: " + e.getMessage());

            return false;
        }
    }

    /**
     * 发送英文短信，带验证
     * @param to 接收手机号
     * @param countryCode 国家/地区代码
     * @param vars 模板变量数据
     * @return 是否发送成功
     */
    public boolean sendEnglishSms(String to, String countryCode, Map<String, String> vars) {
        long startTime = System.currentTimeMillis();

        try {
            // 标准化手机号
            to = phoneValidator.normalizePhoneNumber(to);
            if (countryCode == null || countryCode.isEmpty()) {
                countryCode = "86"; // 默认中国区号
            }

            // 验证手机号
            PhoneValidator.ValidationResult validationResult = phoneValidator.validate(to, countryCode);
            if (!validationResult.isValid()) {
                logger.warn("手机号验证失败: {}-{}, 原因: {}", countryCode, to, validationResult.getMessage());
                smsMetrics.recordValidationFailed(validationResult.getMessage());

                // 记录发送失败
                long processingTime = System.currentTimeMillis() - startTime;
                smsMetrics.recordSmsSent("submail", "english", to, countryCode, false,
                        processingTime, "手机号验证失败: " + validationResult.getMessage());

                return false;
            }

            // 检查黑名单
            if (blacklistedPhoneService.isBlacklisted(to, countryCode)) {
                logger.warn("手机号在黑名单中: {}-{}", countryCode, to);
                smsMetrics.recordBlacklisted(countryCode);

                // 记录发送失败
                long processingTime = System.currentTimeMillis() - startTime;
                smsMetrics.recordSmsSent("submail", "english", to, countryCode, false,
                        processingTime, "手机号在黑名单中");

                return false;
            }

            // 发送短信
            boolean success = cnSmsHttpClient.sendEnglishSms(to, vars);

            // 记录发送结果
            long processingTime = System.currentTimeMillis() - startTime;
            smsMetrics.recordSmsSent("submail", "english", to, countryCode, success,
                    processingTime, success ? null : "短信发送失败");

            return success;
        } catch (Exception e) {
            logger.error("发送英文短信失败: {}-{}", countryCode, to, e);

            // 记录发送失败
            long processingTime = System.currentTimeMillis() - startTime;
            smsMetrics.recordSmsSent("submail", "english", to, countryCode, false,
                    processingTime, "发送异常: " + e.getMessage());

            return false;
        }
    }

    /**
     * 发送国际短信，带验证
     * @param to 接收手机号
     * @param countryCode 国家/地区代码
     * @param content 短信内容
     * @return 是否发送成功
     */
    public boolean sendInternationalSms(String to, String countryCode, String content) {
        long startTime = System.currentTimeMillis();

        try {
            // 标准化手机号
            to = phoneValidator.normalizePhoneNumber(to);
            if (countryCode == null || countryCode.isEmpty()) {
                logger.warn("国际短信必须提供国家/地区代码");

                // 记录发送失败
                long processingTime = System.currentTimeMillis() - startTime;
                smsMetrics.recordSmsSent("submail", "international", to, "unknown", false,
                        processingTime, "未提供国家/地区代码");

                return false;
            }

            // 验证手机号
            PhoneValidator.ValidationResult validationResult = phoneValidator.validate(to, countryCode);
            if (!validationResult.isValid()) {
                logger.warn("手机号验证失败: {}-{}, 原因: {}", countryCode, to, validationResult.getMessage());
                smsMetrics.recordValidationFailed(validationResult.getMessage());

                // 记录发送失败
                long processingTime = System.currentTimeMillis() - startTime;
                smsMetrics.recordSmsSent("submail", "international", to, countryCode, false,
                        processingTime, "手机号验证失败: " + validationResult.getMessage());

                return false;
            }

            // 检查黑名单
            if (blacklistedPhoneService.isBlacklisted(to, countryCode)) {
                logger.warn("手机号在黑名单中: {}-{}", countryCode, to);
                smsMetrics.recordBlacklisted(countryCode);

                // 记录发送失败
                long processingTime = System.currentTimeMillis() - startTime;
                smsMetrics.recordSmsSent("submail", "international", to, countryCode, false,
                        processingTime, "手机号在黑名单中");

                return false;
            }

            // 发送国际短信
            boolean success = interSmsHttpClient.sendByContent(countryCode + to, content);

            // 记录发送结果
            long processingTime = System.currentTimeMillis() - startTime;
            smsMetrics.recordSmsSent("submail", "international", to, countryCode, success,
                    processingTime, success ? null : "短信发送失败");

            return success;
        } catch (Exception e) {
            logger.error("发送国际短信失败: {}-{}", countryCode, to, e);

            // 记录发送失败
            long processingTime = System.currentTimeMillis() - startTime;
            smsMetrics.recordSmsSent("submail", "international", to, countryCode, false,
                    processingTime, "发送异常: " + e.getMessage());

            return false;
        }
    }
}
