package com.media.mail.service.impl;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.media.mail.entity.SmsSendStats;
import com.media.mail.mapper.SmsSendStatsMapper;
import com.media.mail.service.BlacklistedPhoneService;
import com.media.mail.service.SmsSendStatsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 短信发送统计服务实现类
 */
@Service
public class SmsSendStatsServiceImpl implements SmsSendStatsService {

    private static final Logger logger = LoggerFactory.getLogger(SmsSendStatsServiceImpl.class);

    @Autowired
    private SmsSendStatsMapper smsSendStatsMapper;

    @Autowired
    private BlacklistedPhoneService blacklistedPhoneService;

    // 配置参数
    private int failureThreshold = 3; // 24小时内失败次数阈值，超过则加入黑名单
    private int blacklistDuration = 7; // 黑名单持续天数

    public SmsSendStatsServiceImpl() {
        // 从Apollo获取配置
        Config config = ConfigService.getAppConfig();
        failureThreshold = config.getIntProperty("SMS_FAILURE_THRESHOLD", 3);
        blacklistDuration = config.getIntProperty("SMS_BLACKLIST_DURATION_DAYS", 7);

        // 监听配置变更
        config.addChangeListener(changeEvent -> {
            if (changeEvent.isChanged("SMS_FAILURE_THRESHOLD")) {
                failureThreshold = config.getIntProperty("SMS_FAILURE_THRESHOLD", 3);
                logger.info("SMS_FAILURE_THRESHOLD changed to: {}", failureThreshold);
            }
            if (changeEvent.isChanged("SMS_BLACKLIST_DURATION_DAYS")) {
                blacklistDuration = config.getIntProperty("SMS_BLACKLIST_DURATION_DAYS", 7);
                logger.info("SMS_BLACKLIST_DURATION_DAYS changed to: {}", blacklistDuration);
            }
        });
    }

    @Override
    @Transactional
    public boolean recordSmsSent(SmsSendStats stats) {
        if (stats == null) {
            return false;
        }

        // 设置创建和更新时间
        Date now = new Date();
        stats.setCreatedAt(now);
        stats.setUpdatedAt(now);

        // 如果没有设置删除标志，设置为0
        if (stats.getDeleted() == null) {
            stats.setDeleted(0);
        }

        try {
            int rows = smsSendStatsMapper.insert(stats);

            // 如果是失败状态，检查是否需要加入黑名单
            if ("FAILED".equals(stats.getStatus())) {
                checkAndBlacklistIfNeeded(stats.getRecipient(), stats.getCountryCode());
            }

            return rows > 0;
        } catch (Exception e) {
            logger.error("记录短信发送统计失败", e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean updateSmsStatus(String messageId, String status, String errorMessage) {
        if (messageId == null || messageId.isEmpty()) {
            return false;
        }

        try {
            // 查找对应的记录
            SmsSendStats stats = new SmsSendStats();
            stats.setMessageId(messageId);
            stats.setStatus(status);
            stats.setErrorMessage(errorMessage);
            stats.setUpdatedAt(new Date());

            // 更新记录
            int rows = smsSendStatsMapper.updateById(stats);

            // 如果是失败状态，检查是否需要加入黑名单
            if ("FAILED".equals(status)) {
                // 获取完整的记录信息
                List<SmsSendStats> records = smsSendStatsMapper.findByPhoneNumber(stats.getRecipient(), stats.getCountryCode(), 1);
                if (!records.isEmpty()) {
                    checkAndBlacklistIfNeeded(records.get(0).getRecipient(), records.get(0).getCountryCode());
                }
            }

            return rows > 0;
        } catch (Exception e) {
            logger.error("更新短信状态失败", e);
            return false;
        }
    }

    @Override
    public double getSuccessRate(Date startTime, Date endTime) {
        if (startTime == null || endTime == null) {
            return 0.0;
        }

        try {
            int successCount = smsSendStatsMapper.countSuccessInTimeRange(startTime, endTime);
            int failureCount = smsSendStatsMapper.countFailureInTimeRange(startTime, endTime);

            int totalCount = successCount + failureCount;
            if (totalCount == 0) {
                return 100.0; // 没有发送记录，默认为100%成功率
            }

            return (double) successCount / totalCount * 100.0;
        } catch (Exception e) {
            logger.error("计算短信发送成功率失败", e);
            return 0.0;
        }
    }

    @Override
    public boolean shouldBlacklist(String phoneNumber, String countryCode) {
        if (phoneNumber == null || phoneNumber.isEmpty()) {
            return false;
        }

        try {
            // 获取24小时前的时间
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.HOUR, -24);
            Date oneDayAgo = calendar.getTime();
            Date now = new Date();

            // 查询24小时内的失败次数
            int failureCount = smsSendStatsMapper.countFailureByPhoneInTimeRange(
                    phoneNumber, countryCode, oneDayAgo, now);

            return failureCount >= failureThreshold;
        } catch (Exception e) {
            logger.error("检查手机号是否应该被加入黑名单失败", e);
            return false;
        }
    }

    @Override
    public List<SmsSendStats> getPhoneHistory(String phoneNumber, String countryCode, int limit) {
        if (phoneNumber == null || phoneNumber.isEmpty()) {
            return List.of();
        }

        try {
            return smsSendStatsMapper.findByPhoneNumber(phoneNumber, countryCode, limit);
        } catch (Exception e) {
            logger.error("获取手机号发送历史失败", e);
            return List.of();
        }
    }

    /**
     * 检查手机号是否需要加入黑名单，如果需要则加入
     * @param phoneNumber 手机号
     * @param countryCode 国家/地区代码
     */
    private void checkAndBlacklistIfNeeded(String phoneNumber, String countryCode) {
        if (shouldBlacklist(phoneNumber, countryCode)) {
            // 计算黑名单过期时间
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, blacklistDuration);
            Date expiresAt = calendar.getTime();

            // 将手机号加入黑名单
            blacklistedPhoneService.addToBlacklist(
                    phoneNumber,
                    countryCode,
                    "短信发送失败次数过多",
                    expiresAt);

            logger.info("手机号 {}-{} 已加入黑名单，原因：短信发送失败次数过多，过期时间：{}",
                    countryCode, phoneNumber, expiresAt);
        }
    }
}
