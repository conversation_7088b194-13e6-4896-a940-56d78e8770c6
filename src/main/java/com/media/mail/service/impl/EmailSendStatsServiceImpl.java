package com.media.mail.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.media.mail.entity.EmailSendStats;
import com.media.mail.mapper.EmailSendStatsMapper;
import com.media.mail.service.EmailSendStatsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 邮件发送统计服务实现类
 */
@Service
public class EmailSendStatsServiceImpl extends ServiceImpl<EmailSendStatsMapper, EmailSendStats> implements EmailSendStatsService {

    private static final Logger logger = LoggerFactory.getLogger(EmailSendStatsServiceImpl.class);

    @Autowired
    private EmailSendStatsMapper emailSendStatsMapper;

    /**
     * 记录邮件发送统计信息
     * @param stats 邮件发送统计信息
     * @return 是否成功
     */
    @Override
    @Transactional
    public boolean recordEmailSendStats(EmailSendStats stats) {
        if (stats == null) {
            return false;
        }

        // 设置创建时间
        if (stats.getCreatedAt() == null) {
            stats.setCreatedAt(new Date());
        }

        // 设置更新时间
        stats.setUpdatedAt(new Date());

        try {
            return save(stats);
        } catch (Exception e) {
            logger.error("Failed to record email send stats", e);
            return false;
        }
    }

    /**
     * 获取指定时间段内的邮件发送成功率
     * @param provider 邮件提供商
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 成功率（百分比）
     */
    @Override
    public Double getSuccessRate(String provider, Date startTime, Date endTime) {
        return emailSendStatsMapper.getSuccessRate(provider, startTime, endTime);
    }

    /**
     * 获取指定时间段内的邮件发送数量
     * @param provider 邮件提供商
     * @param status 发送状态
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 发送数量
     */
    @Override
    public Integer countByProviderAndStatus(String provider, String status, Date startTime, Date endTime) {
        return emailSendStatsMapper.countByProviderAndStatus(provider, status, startTime, endTime);
    }

    /**
     * 获取指定接收者的邮件发送历史
     * @param recipient 接收者邮箱
     * @param limit 限制数量
     * @return 邮件发送历史列表
     */
    @Override
    public List<EmailSendStats> findByRecipient(String recipient, Integer limit) {
        if (limit == null || limit <= 0) {
            limit = 10; // 默认返回10条记录
        }
        return emailSendStatsMapper.findByRecipient(recipient, limit);
    }

    /**
     * 获取平均处理时间
     * @param provider 邮件提供商
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 平均处理时间（毫秒）
     */
    @Override
    public Double getAverageProcessingTime(String provider, Date startTime, Date endTime) {
        return emailSendStatsMapper.getAverageProcessingTime(provider, startTime, endTime);
    }

    /**
     * 获取各邮件提供商的发送统计
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计结果，键为提供商，值为成功/失败/总数的Map
     */
    @Override
    public Map<String, Map<String, Integer>> getProviderStats(Date startTime, Date endTime) {
        // 查询所有提供商
        LambdaQueryWrapper<EmailSendStats> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(EmailSendStats::getProvider)
                .between(EmailSendStats::getSendTime, startTime, endTime)
                .groupBy(EmailSendStats::getProvider);

        List<EmailSendStats> providers = list(queryWrapper);

        Map<String, Map<String, Integer>> result = new HashMap<>();

        for (EmailSendStats provider : providers) {
            String providerName = provider.getProvider();
            Map<String, Integer> stats = new HashMap<>();

            // 成功数量
            Integer successCount = countByProviderAndStatus(providerName, "SUCCESS", startTime, endTime);
            stats.put("success", successCount != null ? successCount : 0);

            // 失败数量
            Integer failureCount = countByProviderAndStatus(providerName, "FAILURE", startTime, endTime);
            stats.put("failure", failureCount != null ? failureCount : 0);

            // 总数
            stats.put("total", stats.get("success") + stats.get("failure"));

            result.put(providerName, stats);
        }

        return result;
    }

    /**
     * 清理过期的统计数据
     * @param retentionDays 保留天数
     * @return 清理的记录数
     */
    @Override
    @Transactional
    public long cleanupOldStats(int retentionDays) {
        if (retentionDays <= 0) {
            retentionDays = 90; // 默认保留90天
        }

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_YEAR, -retentionDays);
        Date cutoffDate = calendar.getTime();

        LambdaQueryWrapper<EmailSendStats> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.lt(EmailSendStats::getSendTime, cutoffDate);

        long count = count(queryWrapper);
        if (count > 0) {
            // 逻辑删除
            EmailSendStats updateEntity = new EmailSendStats();
            updateEntity.setDeleted(1);
            update(updateEntity, queryWrapper);

            logger.info("Cleaned up {} old email send stats records older than {} days", count, retentionDays);
        }

        return count;
    }
}
