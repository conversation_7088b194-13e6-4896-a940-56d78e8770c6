package com.media.mail.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.media.mail.entity.VerifiedEmail;
import com.media.mail.mapper.VerifiedEmailMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 已验证邮箱服务
 * 用于管理已成功送达的邮箱地址，提高邮箱验证效率
 */
@Service
public class VerifiedEmailService extends ServiceImpl<VerifiedEmailMapper, VerifiedEmail> {

    private static final Logger logger = LoggerFactory.getLogger(VerifiedEmailService.class);

    // 缓存已验证的邮箱，提高查询性能
    private final Set<String> verifiedEmailCache = ConcurrentHashMap.newKeySet();

    // 缓存已验证的域名，提高查询性能
    private final Set<String> verifiedDomainCache = ConcurrentHashMap.newKeySet();

    // 验证缓存的有效期（毫秒）
    private long cacheExpirationMs = TimeUnit.DAYS.toMillis(30); // 默认30天

    // 是否启用已验证邮箱功能
    private boolean verifiedEmailEnabled = true;

    // 是否启用数据库持久化
    private boolean persistenceEnabled = true;

    // 构造函数，从Apollo获取配置
    public VerifiedEmailService() {
        Config config = ConfigService.getAppConfig();
        verifiedEmailEnabled = config.getBooleanProperty("EMAIL_VERIFIED_ENABLED", true);
        persistenceEnabled = config.getBooleanProperty("EMAIL_VERIFIED_PERSISTENCE_ENABLED", true);
        int expirationDays = config.getIntProperty("EMAIL_VERIFIED_EXPIRATION_DAYS", 30);
        cacheExpirationMs = TimeUnit.DAYS.toMillis(expirationDays);

        // 监听配置变更
        config.addChangeListener(changeEvent -> {
            if (changeEvent.isChanged("EMAIL_VERIFIED_ENABLED")) {
                verifiedEmailEnabled = config.getBooleanProperty("EMAIL_VERIFIED_ENABLED", true);
                logger.info("EMAIL_VERIFIED_ENABLED changed to: {}", verifiedEmailEnabled);
            }
            if (changeEvent.isChanged("EMAIL_VERIFIED_PERSISTENCE_ENABLED")) {
                persistenceEnabled = config.getBooleanProperty("EMAIL_VERIFIED_PERSISTENCE_ENABLED", true);
                logger.info("EMAIL_VERIFIED_PERSISTENCE_ENABLED changed to: {}", persistenceEnabled);
            }
            if (changeEvent.isChanged("EMAIL_VERIFIED_EXPIRATION_DAYS")) {
                int days = config.getIntProperty("EMAIL_VERIFIED_EXPIRATION_DAYS", 30);
                cacheExpirationMs = TimeUnit.DAYS.toMillis(days);
                logger.info("EMAIL_VERIFIED_EXPIRATION_DAYS changed to: {}", days);
            }
        });
    }

    /**
     * 初始化缓存
     */
    @Scheduled(fixedDelay = 3600000) // 每5分钟刷新一次缓存
    public void initVerifiedCache() {
        if (!verifiedEmailEnabled) {
            return;
        }

        try {
            // 从数据库加载有效的域名
            List<VerifiedEmail> activeVerified = baseMapper.findAllActive();
            Set<String> domainSet = activeVerified.stream()
                    .map(VerifiedEmail::getDomain)
                    .filter(domain -> domain != null && !domain.isEmpty())
                    .collect(Collectors.toSet());

            // 更新缓存
            verifiedDomainCache.clear();
            verifiedDomainCache.addAll(domainSet);

            // 更新邮箱缓存
            Set<String> emailSet = activeVerified.stream()
                    .map(VerifiedEmail::getEmail)
                    .filter(email -> email != null && !email.isEmpty())
                    .collect(Collectors.toSet());
            verifiedEmailCache.clear();
            verifiedEmailCache.addAll(emailSet);

            logger.info("Refreshed verified cache with {} emails and {} domains",
                    verifiedEmailCache.size(), verifiedDomainCache.size());
        } catch (Exception e) {
            logger.error("Failed to refresh verified cache", e);
        }
    }

    /**
     * 清理过期的验证记录
     */
    /**
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
    @Transactional
    public void cleanExpiredEmails() {
        if (!verifiedEmailEnabled || !persistenceEnabled) {
            return;
        }

        try {
            int count = baseMapper.cleanExpiredEmails();
            if (count > 0) {
                logger.info("Cleaned {} expired verified email records", count);
                // 重新加载缓存
                initVerifiedCache();
            }
        } catch (Exception e) {
            logger.error("Failed to clean expired verified emails", e);
        }
    }*/

    /**
     * 添加已验证的邮箱
     * @param email 邮箱地址
     */
    @Transactional
    public void addVerifiedEmail(String email) {
        addVerifiedEmail(email, "manual");
    }

    /**
     * 添加已验证的邮箱
     * @param email 邮箱地址
     * @param source 验证来源
     */
    @Transactional
    public void addVerifiedEmail(String email, String source) {
        if (email == null || email.isEmpty() || !verifiedEmailEnabled) {
            return;
        }

        String normalizedEmail = email.toLowerCase();
        verifiedEmailCache.add(normalizedEmail);

        // 提取域名
        String domain = extractDomain(normalizedEmail);
        if (domain != null && !domain.isEmpty()) {
            verifiedDomainCache.add(domain);
        }

        // 如果启用了持久化，将记录保存到数据库
        if (persistenceEnabled && domain != null) {
            try {
                Date now = new Date();

                // 使用 saveOrUpdate 方法避免唯一约束冲突
                LambdaQueryWrapper<VerifiedEmail> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(VerifiedEmail::getEmail, normalizedEmail);
                VerifiedEmail existingRecord = getOne(queryWrapper, false);

                if (existingRecord != null) {
                    // 更新现有记录
                    existingRecord.setLastVerifiedAt(now);
                    existingRecord.setVerifyCount(existingRecord.getVerifyCount() + 1);
                    existingRecord.setSource(source); // 更新来源

                    // 更新过期时间
                    Date newExpiresAt = new Date(now.getTime() + cacheExpirationMs);
                    existingRecord.setExpiresAt(newExpiresAt);

                    // 确保未被标记为删除
                    existingRecord.setDeleted(0);

                    updateById(existingRecord);
                    logger.debug("Updated verified email: {} (source: {})", normalizedEmail, source);
                } else {
                    // 创建新记录
                    Date expiresAt = new Date(now.getTime() + cacheExpirationMs);
                    VerifiedEmail newRecord = VerifiedEmail.builder()
                            .email(normalizedEmail)
                            .domain(domain)
                            .source(source)
                            .verifiedAt(now)
                            .lastVerifiedAt(now)
                            .verifyCount(1)
                            .expiresAt(expiresAt)
                            .deleted(0)
                            .build();

                    // 使用 saveOrUpdate 而不是 save
                    // 避免使用过时的方法
                    boolean exists = count(queryWrapper) > 0;
                    if (exists) {
                        update(newRecord, queryWrapper);
                    } else {
                        save(newRecord);
                    }
                    logger.debug("Added new verified email: {} (source: {})", normalizedEmail, source);
                }
            } catch (Exception e) {
                // 即使出错也不影响缓存中的邮箱状态
                logger.warn("Failed to save verified email to database (but still cached in memory): {}", normalizedEmail, e);
            }
        }

        logger.debug("Added verified email: {} (source: {})", normalizedEmail, source);
    }

    /**
     * 检查邮箱是否已验证
     * @param email 邮箱地址
     * @return 是否已验证
     */
    public boolean isEmailVerified(String email) {
        if (!verifiedEmailEnabled || email == null || email.isEmpty()) {
            return false;
        }

        String normalizedEmail = email.toLowerCase();

        // 先检查缓存
        if (verifiedEmailCache.contains(normalizedEmail)) {
            return true;
        }

        // 缓存中没有，查询数据库
        if (persistenceEnabled) {
            try {
                VerifiedEmail record = baseMapper.findByEmail(normalizedEmail);
                if (record != null && !record.isExpired()) {
                    // 更新缓存
                    verifiedEmailCache.add(normalizedEmail);
                    return true;
                }
            } catch (Exception e) {
                logger.error("Failed to check verified email in database: {}", normalizedEmail, e);
            }
        }

        return false;
    }

    /**
     * 检查域名是否已验证
     * @param domain 域名
     * @return 是否已验证
     */
    public boolean isDomainVerified(String domain) {
        if (!verifiedEmailEnabled || domain == null || domain.isEmpty()) {
            return false;
        }

        String normalizedDomain = domain.toLowerCase();

        // 先检查缓存
        if (verifiedDomainCache.contains(normalizedDomain)) {
            return true;
        }

        // 缓存中没有，查询数据库
        if (persistenceEnabled) {
            try {
                int count = baseMapper.countByDomain(normalizedDomain);
                if (count > 0) {
                    // 更新缓存
                    verifiedDomainCache.add(normalizedDomain);
                    return true;
                }
            } catch (Exception e) {
                logger.error("Failed to check verified domain in database: {}", normalizedDomain, e);
            }
        }

        return false;
    }

    /**
     * 从邮箱地址中提取域名
     * @param email 邮箱地址
     * @return 域名
     */
    private String extractDomain(String email) {
        if (email == null || email.isEmpty() || !email.contains("@")) {
            return null;
        }

        return email.substring(email.indexOf('@') + 1).toLowerCase();
    }

    /**
     * 清理缓存
     */
    public void clearCache() {
        verifiedEmailCache.clear();
        verifiedDomainCache.clear();
        logger.info("Verified email cache cleared");
    }

    /**
     * 删除邮箱验证记录
     * @param email 邮箱地址
     * @return 是否删除成功
     */
    @Transactional
    public boolean removeVerifiedEmail(String email) {
        if (email == null || email.isEmpty()) {
            return false;
        }

        String normalizedEmail = email.toLowerCase();

        // 从缓存中移除
        verifiedEmailCache.remove(normalizedEmail);

        // 从数据库中移除
        if (persistenceEnabled) {
            try {
                VerifiedEmail record = baseMapper.findByEmail(normalizedEmail);
                if (record != null) {
                    return removeById(record.getId());
                }
            } catch (Exception e) {
                logger.error("Failed to remove verified email from database: {}", normalizedEmail, e);
            }
        }

        return true;
    }
}
