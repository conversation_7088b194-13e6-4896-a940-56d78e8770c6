package com.media.mail.service;

import com.media.mail.entity.SmsSendStats;

import java.util.Date;
import java.util.List;

/**
 * 短信发送统计服务接口
 */
public interface SmsSendStatsService {

    /**
     * 记录短信发送
     * @param stats 短信发送统计信息
     * @return 是否成功
     */
    boolean recordSmsSent(SmsSendStats stats);

    /**
     * 更新短信发送状态
     * @param messageId 短信ID
     * @param status 状态
     * @param errorMessage 错误信息
     * @return 是否成功
     */
    boolean updateSmsStatus(String messageId, String status, String errorMessage);

    /**
     * 获取指定时间段内的发送成功率
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 成功率（0-100）
     */
    double getSuccessRate(Date startTime, Date endTime);

    /**
     * 检查手机号是否应该被加入黑名单
     * @param phoneNumber 手机号
     * @param countryCode 国家/地区代码
     * @return 是否应该被加入黑名单
     */
    boolean shouldBlacklist(String phoneNumber, String countryCode);

    /**
     * 获取指定手机号的发送记录
     * @param phoneNumber 手机号
     * @param countryCode 国家/地区代码
     * @param limit 限制数量
     * @return 发送记录列表
     */
    List<SmsSendStats> getPhoneHistory(String phoneNumber, String countryCode, int limit);
}
