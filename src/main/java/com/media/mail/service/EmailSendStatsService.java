package com.media.mail.service;

import com.media.mail.entity.EmailSendStats;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 邮件发送统计服务接口
 */
public interface EmailSendStatsService {

    /**
     * 记录邮件发送统计信息
     * @param stats 邮件发送统计信息
     * @return 是否成功
     */
    boolean recordEmailSendStats(EmailSendStats stats);

    /**
     * 获取指定时间段内的邮件发送成功率
     * @param provider 邮件提供商
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 成功率（百分比）
     */
    Double getSuccessRate(String provider, Date startTime, Date endTime);

    /**
     * 获取指定时间段内的邮件发送数量
     * @param provider 邮件提供商
     * @param status 发送状态
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 发送数量
     */
    Integer countByProviderAndStatus(String provider, String status, Date startTime, Date endTime);

    /**
     * 获取指定接收者的邮件发送历史
     * @param recipient 接收者邮箱
     * @param limit 限制数量
     * @return 邮件发送历史列表
     */
    List<EmailSendStats> findByRecipient(String recipient, Integer limit);

    /**
     * 获取平均处理时间
     * @param provider 邮件提供商
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 平均处理时间（毫秒）
     */
    Double getAverageProcessingTime(String provider, Date startTime, Date endTime);

    /**
     * 获取各邮件提供商的发送统计
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计结果，键为提供商，值为成功/失败/总数的Map
     */
    Map<String, Map<String, Integer>> getProviderStats(Date startTime, Date endTime);

    /**
     * 清理过期的统计数据
     * @param retentionDays 保留天数
     * @return 清理的记录数
     */
    long cleanupOldStats(int retentionDays);
}
