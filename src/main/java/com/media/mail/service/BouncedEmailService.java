package com.media.mail.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.media.mail.entity.BlacklistedEmail;
import com.media.mail.mapper.BlacklistedEmailMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 退信邮箱管理服务
 * 用于管理硬退信和投诉的邮箱地址黑名单
 */
@Service
public class BouncedEmailService {

    private static final Logger logger = LoggerFactory.getLogger(BouncedEmailService.class);

    private boolean blacklistEnabled = true;

    // 缓存黑名单邮箱，提高查询性能
    private final Set<String> blacklistCache = ConcurrentHashMap.newKeySet();

    @Autowired
    private BlacklistedEmailMapper blacklistedEmailMapper;

    // 初始化时加载黑名单
    public BouncedEmailService() {
        // 从Apollo获取配置
        Config config = ConfigService.getAppConfig();
        blacklistEnabled = config.getBooleanProperty("EMAIL_BLACKLIST_ENABLED", true);

        // 监听配置变更
        config.addChangeListener(changeEvent -> {
            if (changeEvent.isChanged("EMAIL_BLACKLIST_ENABLED")) {
                blacklistEnabled = config.getBooleanProperty("EMAIL_BLACKLIST_ENABLED", true);
                logger.info("EMAIL_BLACKLIST_ENABLED changed to: {}", blacklistEnabled);
            }
        });
    }

    /**
     * 初始化缓存
     */
    @Scheduled(fixedDelay = 3600000) // 每5分钟刷新一次缓存
    public void initBlacklistCache() {
        try {
            List<BlacklistedEmail> activeBlacklist = blacklistedEmailMapper.findAllActive();
            Set<String> emailSet = activeBlacklist.stream()
                    .map(item -> item.getEmail().toLowerCase())
                    .collect(Collectors.toSet());

            // 更新缓存
            blacklistCache.clear();
            blacklistCache.addAll(emailSet);

            logger.info("Refreshed blacklist cache with {} emails", blacklistCache.size());
        } catch (Exception e) {
            logger.error("Failed to refresh blacklist cache", e);
        }
    }

    /**
     * 检查邮箱是否在黑名单中
     * @param email 邮箱地址
     * @return 是否在黑名单中
     */
    public boolean isBlacklisted(String email) {
        if (!blacklistEnabled || email == null || email.isEmpty()) {
            return false;
        }

        String normalizedEmail = email.toLowerCase();

        // 先检查缓存
        if (blacklistCache.contains(normalizedEmail)) {
            return true;
        }

        // 缓存中没有，查询数据库
        BlacklistedEmail blacklistedEmail = blacklistedEmailMapper.findByEmail(normalizedEmail);
        if (blacklistedEmail != null && !blacklistedEmail.isExpired()) {
            // 更新缓存
            blacklistCache.add(normalizedEmail);
            return true;
        }

        return false;
    }

    /**
     * 将邮箱添加到黑名单
     * @param email 邮箱地址
     * @param reason 添加原因
     */
    @Transactional
    public void addToBlacklist(String email, String reason) {
        if (email == null || email.isEmpty()) {
            return;
        }

        String normalizedEmail = email.toLowerCase();

        // 检查是否已存在
        BlacklistedEmail existing = blacklistedEmailMapper.findByEmail(normalizedEmail);

        if (existing != null) {
            // 更新现有记录
            existing.incrementBounceCount();
            existing.setReason(reason);
            blacklistedEmailMapper.updateById(existing);
            logger.info("Updated existing blacklist entry: {} (reason: {}, bounce count: {})",
                    email, reason, existing.getBounceCount());
        } else {
            // 创建新记录
            BlacklistedEmail newEntry = BlacklistedEmail.builder()
                    .email(normalizedEmail)
                    .reason(reason)
                    .createdAt(new Date())
                    .permanent(true)
                    .bounceCount(1)
                    .deleted(0)
                    .build();

            blacklistedEmailMapper.insert(newEntry);
            logger.info("Added email to blacklist: {} (reason: {})", email, reason);
        }

        // 更新缓存
        blacklistCache.add(normalizedEmail);
    }

    /**
     * 将邮箱添加到黑名单，带过期时间
     * @param email 邮箱地址
     * @param reason 添加原因
     * @param expiresAt 过期时间
     */
    @Transactional
    public void addToBlacklist(String email, String reason, Date expiresAt) {
        if (email == null || email.isEmpty()) {
            return;
        }

        String normalizedEmail = email.toLowerCase();

        // 检查是否已存在
        BlacklistedEmail existing = blacklistedEmailMapper.findByEmail(normalizedEmail);

        if (existing != null) {
            // 更新现有记录
            existing.incrementBounceCount();
            existing.setReason(reason);
            existing.setPermanent(false);
            existing.setExpiresAt(expiresAt);
            blacklistedEmailMapper.updateById(existing);
            logger.info("Updated existing blacklist entry: {} (reason: {}, expires: {})",
                    email, reason, expiresAt);
        } else {
            // 创建新记录
            BlacklistedEmail newEntry = BlacklistedEmail.builder()
                    .email(normalizedEmail)
                    .reason(reason)
                    .createdAt(new Date())
                    .permanent(false)
                    .expiresAt(expiresAt)
                    .bounceCount(1)
                    .deleted(0)
                    .build();

            blacklistedEmailMapper.insert(newEntry);
            logger.info("Added email to blacklist: {} (reason: {}, expires: {})", email, reason, expiresAt);
        }

        // 更新缓存
        blacklistCache.add(normalizedEmail);
    }

    /**
     * 从黑名单中移除邮箱
     * @param email 邮箱地址
     */
    @Transactional
    public void removeFromBlacklist(String email) {
        if (email == null || email.isEmpty()) {
            return;
        }

        String normalizedEmail = email.toLowerCase();

        // 逻辑删除记录
        LambdaUpdateWrapper<BlacklistedEmail> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(BlacklistedEmail::getEmail, normalizedEmail);

        int rows = blacklistedEmailMapper.delete(updateWrapper);
        if (rows > 0) {
            logger.info("Removed email from blacklist: {}", email);

            // 更新缓存
            blacklistCache.remove(normalizedEmail);
        }
    }

    /**
     * 获取黑名单中的所有邮箱
     * @return 黑名单邮箱集合
     */
    public Set<String> getBlacklistedEmails() {
        List<BlacklistedEmail> activeBlacklist = blacklistedEmailMapper.findAllActive();
        return activeBlacklist.stream()
                .map(BlacklistedEmail::getEmail)
                .collect(Collectors.toSet());
    }

    /**
     * 获取黑名单大小
     * @return 黑名单大小
     */
    public int getBlacklistSize() {
        LambdaQueryWrapper<BlacklistedEmail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BlacklistedEmail::getDeleted, 0);
        return blacklistedEmailMapper.selectCount(queryWrapper).intValue();
    }

    /**
     * 定期清理过期的黑名单记录（每天）
     */
    /**
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
    @Transactional
    public void cleanupExpiredEntries() {
        int rows = blacklistedEmailMapper.cleanExpiredBlacklist();
        if (rows > 0) {
            logger.info("Cleaned up {} expired blacklist entries", rows);
            // 刷新缓存
            initBlacklistCache();
        }
    }*/
}
