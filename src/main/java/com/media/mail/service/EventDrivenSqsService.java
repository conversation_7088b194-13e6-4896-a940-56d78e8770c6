package com.media.mail.service;

import com.media.mail.controller.SESNotificationController;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import software.amazon.awssdk.services.sqs.SqsAsyncClient;
import software.amazon.awssdk.services.sqs.model.DeleteMessageRequest;
import software.amazon.awssdk.services.sqs.model.Message;
import software.amazon.awssdk.services.sqs.model.ReceiveMessageRequest;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 真正的事件驱动式 SQS 服务
 * 使用 AWS SDK v2 和 CompletableFuture 实现异步非阻塞事件驱动模式
 */
@Service
@ConditionalOnProperty(prefix = "aws.sqs", name = "enabled", havingValue = "true", matchIfMissing = true)
public class EventDrivenSqsService implements InitializingBean, DisposableBean {

    private static final Logger logger = LoggerFactory.getLogger(EventDrivenSqsService.class);

    @Autowired
    private SqsAsyncClient sqsAsyncClient;

    @Autowired
    private SESNotificationController sesNotificationController;

    @Value("${aws.sqs.notification-queue-url:}")
    private String queueUrl;

    @Value("${aws.sqs.batch-size:10}")
    private int batchSize;

    @Value("${aws.sqs.visibility-timeout:30}")
    private int visibilityTimeout;

    @Value("${aws.sqs.concurrency:5}")
    private int concurrency;

    @Value("${aws.sqs.long-polling-seconds:20}")
    private int longPollingSeconds;

    private final AtomicBoolean running = new AtomicBoolean(true);
    private final AtomicInteger activeProcessingCount = new AtomicInteger(0);
    private ExecutorService executorService;

    /**
     * 初始化方法，在服务启动时自动调用
     */
    @Override
    public void afterPropertiesSet() throws Exception {
        if (!StringUtils.hasText(queueUrl)) {
            logger.info("SQS queue URL not configured, event-driven SQS service will not start");
            return;
        }

        logger.info("Starting event-driven SQS service with concurrency: {}", concurrency);
        executorService = Executors.newFixedThreadPool(concurrency);

        // 为每个并发级别启动一个消息接收器
        for (int i = 0; i < concurrency; i++) {
            final int receiverId = i;
            CompletableFuture.runAsync(() -> {
                logger.info("Starting message receiver #{}", receiverId);
                startMessageReceiver(receiverId);
            }, executorService);
        }
    }

    /**
     * 销毁方法，在服务关闭时自动调用
     */
    @Override
    public void destroy() throws Exception {
        logger.info("Shutting down event-driven SQS service");
        running.set(false);

        if (executorService != null) {
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(30, java.util.concurrent.TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    /**
     * 启动消息接收器
     * 使用递归异步调用实现连续接收，无需轮询
     */
    private void startMessageReceiver(int receiverId) {
        if (!running.get()) {
            logger.info("Message receiver #{} stopping as service is shutting down", receiverId);
            return;
        }

        // 检查当前活跃处理数量，实现背压控制
        if (activeProcessingCount.get() >= concurrency) {
            logger.info("Receiver #{} backing off due to high processing load (active={})",
                    receiverId, activeProcessingCount.get());

            // 短暂延迟后重试
            CompletableFuture.delayedExecutor(1, java.util.concurrent.TimeUnit.SECONDS, executorService)
                    .execute(() -> startMessageReceiver(receiverId));
            return;
        }

        // 创建接收消息请求
        ReceiveMessageRequest receiveRequest = ReceiveMessageRequest.builder()
                .queueUrl(queueUrl)
                .maxNumberOfMessages(batchSize)
                .waitTimeSeconds(longPollingSeconds) // 长轮询，最多等待20秒
                .visibilityTimeout(visibilityTimeout)
                .build();

        // 异步接收消息
        sqsAsyncClient.receiveMessage(receiveRequest)
                .thenAccept(response -> {
                    List<Message> messages = response.messages();

                    if (!messages.isEmpty()) {
                        logger.info("Receiver #{} received {} messages", receiverId, messages.size());

                        // 处理每条消息
                        messages.forEach(message -> processMessageAsync(message, receiverId));
                    }

                    // 无论是否收到消息，都继续接收下一批
                    // 这是事件驱动模式的关键：递归异步调用，而不是定时轮询
                    startMessageReceiver(receiverId);
                })
                .exceptionally(e -> {
                    logger.error("Error receiving messages in receiver #{}", receiverId, e);

                    // 发生错误时，短暂延迟后重试
                    CompletableFuture.delayedExecutor(5, java.util.concurrent.TimeUnit.SECONDS, executorService)
                            .execute(() -> startMessageReceiver(receiverId));
                    return null;
                });
    }

    /**
     * 异步处理单条消息
     */
    private void processMessageAsync(Message message, int receiverId) {
        String messageId = message.messageId();
        String receiptHandle = message.receiptHandle();

        // 增加活跃处理计数
        activeProcessingCount.incrementAndGet();

        try {
            logger.debug("Receiver #{} processing message: {}", receiverId, messageId);

            // 处理消息
            processSqsMessage(message.body(), messageId)
                    .thenAccept(success -> {
                        if (success) {
                            // 处理成功，删除消息
                            deleteMessageAsync(receiptHandle, messageId, receiverId);
                        } else {
                            logger.warn("Receiver #{} failed to process message: {}, will retry after visibility timeout",
                                    receiverId, messageId);
                        }
                        // 减少活跃处理计数
                        activeProcessingCount.decrementAndGet();
                    });
        } catch (Exception e) {
            logger.error("Receiver #{} encountered error processing message: {}", receiverId, messageId, e);
            // 确保在异常情况下也减少活跃处理计数
            activeProcessingCount.decrementAndGet();
        }
    }

    /**
     * 处理 SQS 消息内容
     * 返回 CompletableFuture<Boolean> 表示处理结果
     */
    private CompletableFuture<Boolean> processSqsMessage(String messageBody, String messageId) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 将消息传递给 SESNotificationController 处理
                sesNotificationController.processNotificationPayload(messageBody);
                logger.info("Successfully processed SQS message: {}", messageId);
                return true;
            } catch (IOException e) {
                logger.error("Failed to process SQS message due to IO error: {}", messageId, e);
                return false;
            } catch (Exception e) {
                logger.error("Failed to process SQS message: {}", messageId, e);
                return false;
            }
        }, executorService);
    }

    /**
     * 异步删除已处理的消息
     */
    private void deleteMessageAsync(String receiptHandle, String messageId, int receiverId) {
        DeleteMessageRequest deleteRequest = DeleteMessageRequest.builder()
                .queueUrl(queueUrl)
                .receiptHandle(receiptHandle)
                .build();

        sqsAsyncClient.deleteMessage(deleteRequest)
                .thenAccept(response ->
                    logger.debug("Receiver #{} successfully deleted message: {}", receiverId, messageId))
                .exceptionally(e -> {
                    logger.error("Receiver #{} failed to delete message: {}", receiverId, messageId, e);
                    return null;
                });
    }
}
