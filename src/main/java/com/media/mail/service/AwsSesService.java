//package com.example.mail.service;
//
//
//import com.amazonaws.services.simpleemail.AmazonSimpleEmailService;
//import com.amazonaws.services.simpleemail.model.*;
//import com.example.mail.exception.AwsSesClientException;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//@Service
//public class AwsSesService {
//    private static final Logger logger = LoggerFactory.getLogger(AwsSesService.class);
//
//
//    private static final String CHARSET = "UTF-8";
//
//    private static final String SUBJECT = "Test from our application";
//
//    @Autowired
//    private AmazonSimpleEmailService emailService;
//
//    public void sendEmail(String from, String to,String body) {
//        try {
//
//            // The time for request/response round trip to aws in milliseconds
//            int requestTimeout = 3000;
//            SendEmailRequest request = new SendEmailRequest()
//                    .withDestination(
//                            new Destination().withToAddresses(to))
//                    .withMessage(new Message()
//                            .withBody(new Body()
//                                    .withText(new Content()
//                                            .withCharset(CHARSET).withData(body)))
//                            .withSubject(new Content()
//                                    .withCharset(CHARSET).withData(SUBJECT)))
//                    .withSource(from).withSdkRequestTimeout(requestTimeout);
//            emailService.sendEmail(request);
//        } catch (RuntimeException e) {
//            logger.error("Error occurred sending email to {} ", to, e);
//            throw new AwsSesClientException("Failed to send email ", e);
//        }
//    }
//
//}
