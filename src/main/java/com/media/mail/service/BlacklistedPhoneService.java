package com.media.mail.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.media.mail.entity.BlacklistedPhone;
import com.media.mail.mapper.BlacklistedPhoneMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 黑名单手机号管理服务
 */
@Service
public class BlacklistedPhoneService {

    private static final Logger logger = LoggerFactory.getLogger(BlacklistedPhoneService.class);

    private boolean blacklistEnabled = true;

    // 缓存黑名单手机号，提高查询性能
    private final Set<String> blacklistCache = ConcurrentHashMap.newKeySet();

    @Autowired
    private BlacklistedPhoneMapper blacklistedPhoneMapper;

    // 初始化时加载黑名单
    public BlacklistedPhoneService() {
        // 从Apollo获取配置
        Config config = ConfigService.getAppConfig();
        blacklistEnabled = config.getBooleanProperty("PHONE_BLACKLIST_ENABLED", true);

        // 监听配置变更
        config.addChangeListener(changeEvent -> {
            if (changeEvent.isChanged("PHONE_BLACKLIST_ENABLED")) {
                blacklistEnabled = config.getBooleanProperty("PHONE_BLACKLIST_ENABLED", true);
                logger.info("PHONE_BLACKLIST_ENABLED changed to: {}", blacklistEnabled);
            }
        });
    }

    /**
     * 初始化缓存
     */
    @Scheduled(fixedDelay = 3600000) // 每5分钟刷新一次缓存
    public void initBlacklistCache() {
        try {
            List<BlacklistedPhone> activeBlacklist = blacklistedPhoneMapper.findAllActive();
            Set<String> phoneSet = activeBlacklist.stream()
                    .map(item -> item.getCountryCode() + "-" + item.getPhoneNumber())
                    .collect(Collectors.toSet());

            // 更新缓存
            blacklistCache.clear();
            blacklistCache.addAll(phoneSet);

            logger.info("Refreshed phone blacklist cache with {} numbers", blacklistCache.size());
        } catch (Exception e) {
            logger.error("Failed to refresh phone blacklist cache", e);
        }
    }

    /**
     * 检查手机号是否在黑名单中
     * @param phoneNumber 手机号
     * @param countryCode 国家/地区代码
     * @return 是否在黑名单中
     */
    public boolean isBlacklisted(String phoneNumber, String countryCode) {
        if (!blacklistEnabled || phoneNumber == null || phoneNumber.isEmpty()) {
            return false;
        }

        // 标准化手机号
        phoneNumber = normalizePhoneNumber(phoneNumber);
        if (countryCode == null || countryCode.isEmpty()) {
            countryCode = "86"; // 默认中国区号
        }

        String cacheKey = countryCode + "-" + phoneNumber;

        // 先检查缓存
        if (blacklistCache.contains(cacheKey)) {
            return true;
        }

        // 缓存中没有，查询数据库
        BlacklistedPhone blacklistedPhone = blacklistedPhoneMapper.findByPhoneNumber(phoneNumber, countryCode);
        if (blacklistedPhone != null && !blacklistedPhone.isExpired()) {
            // 更新缓存
            blacklistCache.add(cacheKey);
            return true;
        }

        return false;
    }

    /**
     * 将手机号添加到黑名单
     * @param phoneNumber 手机号
     * @param countryCode 国家/地区代码
     * @param reason 添加原因
     */
    @Transactional
    public void addToBlacklist(String phoneNumber, String countryCode, String reason) {
        if (phoneNumber == null || phoneNumber.isEmpty()) {
            return;
        }

        // 标准化手机号
        phoneNumber = normalizePhoneNumber(phoneNumber);
        if (countryCode == null || countryCode.isEmpty()) {
            countryCode = "86"; // 默认中国区号
        }

        // 检查是否已存在
        BlacklistedPhone existing = blacklistedPhoneMapper.findByPhoneNumber(phoneNumber, countryCode);

        if (existing != null) {
            // 更新现有记录
            existing.incrementFailureCount();
            existing.setReason(reason);
            blacklistedPhoneMapper.updateById(existing);
            logger.info("Updated existing blacklist entry: {}-{} (reason: {}, failure count: {})",
                    countryCode, phoneNumber, reason, existing.getFailureCount());
        } else {
            // 创建新记录
            BlacklistedPhone newEntry = BlacklistedPhone.builder()
                    .phoneNumber(phoneNumber)
                    .countryCode(countryCode)
                    .reason(reason)
                    .createdAt(new Date())
                    .permanent(true)
                    .failureCount(1)
                    .deleted(0)
                    .build();

            blacklistedPhoneMapper.insert(newEntry);
            logger.info("Added phone to blacklist: {}-{} (reason: {})", countryCode, phoneNumber, reason);
        }

        // 更新缓存
        blacklistCache.add(countryCode + "-" + phoneNumber);
    }

    /**
     * 将手机号添加到黑名单，带过期时间
     * @param phoneNumber 手机号
     * @param countryCode 国家/地区代码
     * @param reason 添加原因
     * @param expiresAt 过期时间
     */
    @Transactional
    public void addToBlacklist(String phoneNumber, String countryCode, String reason, Date expiresAt) {
        if (phoneNumber == null || phoneNumber.isEmpty()) {
            return;
        }

        // 标准化手机号
        phoneNumber = normalizePhoneNumber(phoneNumber);
        if (countryCode == null || countryCode.isEmpty()) {
            countryCode = "86"; // 默认中国区号
        }

        // 检查是否已存在
        BlacklistedPhone existing = blacklistedPhoneMapper.findByPhoneNumber(phoneNumber, countryCode);

        if (existing != null) {
            // 更新现有记录
            existing.incrementFailureCount();
            existing.setReason(reason);
            existing.setPermanent(false);
            existing.setExpiresAt(expiresAt);
            blacklistedPhoneMapper.updateById(existing);
            logger.info("Updated existing blacklist entry: {}-{} (reason: {}, expires: {})",
                    countryCode, phoneNumber, reason, expiresAt);
        } else {
            // 创建新记录
            BlacklistedPhone newEntry = BlacklistedPhone.builder()
                    .phoneNumber(phoneNumber)
                    .countryCode(countryCode)
                    .reason(reason)
                    .createdAt(new Date())
                    .permanent(false)
                    .expiresAt(expiresAt)
                    .failureCount(1)
                    .deleted(0)
                    .build();

            blacklistedPhoneMapper.insert(newEntry);
            logger.info("Added phone to blacklist: {}-{} (reason: {}, expires: {})",
                    countryCode, phoneNumber, reason, expiresAt);
        }

        // 更新缓存
        blacklistCache.add(countryCode + "-" + phoneNumber);
    }

    /**
     * 从黑名单中移除手机号
     * @param phoneNumber 手机号
     * @param countryCode 国家/地区代码
     */
    @Transactional
    public void removeFromBlacklist(String phoneNumber, String countryCode) {
        if (phoneNumber == null || phoneNumber.isEmpty()) {
            return;
        }

        // 标准化手机号
        phoneNumber = normalizePhoneNumber(phoneNumber);
        if (countryCode == null || countryCode.isEmpty()) {
            countryCode = "86"; // 默认中国区号
        }

        // 逻辑删除记录
        LambdaUpdateWrapper<BlacklistedPhone> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(BlacklistedPhone::getPhoneNumber, phoneNumber)
                     .eq(BlacklistedPhone::getCountryCode, countryCode);

        int rows = blacklistedPhoneMapper.delete(updateWrapper);
        if (rows > 0) {
            logger.info("Removed phone from blacklist: {}-{}", countryCode, phoneNumber);

            // 更新缓存
            blacklistCache.remove(countryCode + "-" + phoneNumber);
        }
    }

    /**
     * 获取黑名单大小
     * @return 黑名单大小
     */
    public int getBlacklistSize() {
        LambdaQueryWrapper<BlacklistedPhone> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BlacklistedPhone::getDeleted, 0);
        return blacklistedPhoneMapper.selectCount(queryWrapper).intValue();
    }

    /**
     * 定期清理过期的黑名单记录（每天）
     */
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
    @Transactional
    public void cleanupExpiredEntries() {
        int rows = blacklistedPhoneMapper.cleanExpiredBlacklist();
        if (rows > 0) {
            logger.info("Cleaned up {} expired phone blacklist entries", rows);
            // 刷新缓存
            initBlacklistCache();
        }
    }

    /**
     * 标准化手机号（去除空格、破折号等）
     * @param phoneNumber 原始手机号
     * @return 标准化后的手机号
     */
    private String normalizePhoneNumber(String phoneNumber) {
        if (phoneNumber == null) {
            return "";
        }
        // 移除所有非数字字符
        return phoneNumber.replaceAll("[^0-9]", "");
    }
}
