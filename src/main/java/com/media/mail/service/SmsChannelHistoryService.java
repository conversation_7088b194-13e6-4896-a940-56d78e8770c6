package com.media.mail.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.media.mail.config.SmsChannelConfig;
import com.media.mail.entity.SmsChannelHistory;
import com.media.mail.mapper.SmsChannelHistoryMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

@Slf4j
@Service
@RequiredArgsConstructor
public class SmsChannelHistoryService {
    private final SmsChannelHistoryMapper smsChannelHistoryMapper;
    private final SmsChannelConfig smsChannelConfig;

    public void recordSmsAttempt(String phone, String prefix, int channelType, boolean success) {
        SmsChannelHistory history = new SmsChannelHistory();
        history.setPhone(phone);
        history.setPrefix(prefix);
        history.setChannelType(channelType);
        history.setSuccess(success);
        history.setSendTime(LocalDateTime.now());
        smsChannelHistoryMapper.insert(history);
    }

    public Integer suggestChannel(String phone, String prefix) {
        // 获取所有可用的通道
        java.util.List<Integer> availableChannels = smsChannelConfig.getAvailableChannels();

        if (availableChannels.isEmpty()) {
            log.error("所有短信通道都被禁用！");
            return smsChannelConfig.getDefaultChannel(); // 返回默认通道，但可能会失败
        }

        // 如果只有一个可用通道，直接使用
        if (availableChannels.size() == 1) {
            int onlyChannel = availableChannels.get(0);
            log.info("只有一个可用通道: {} ({})", onlyChannel, smsChannelConfig.getChannelName(onlyChannel));
            return onlyChannel;
        }

        // 检查是否有前缀优先通道配置
        Integer preferredChannel = smsChannelConfig.getPreferredChannelForPrefix(prefix);
        if (preferredChannel != null && smsChannelConfig.isChannelEnabled(preferredChannel)) {
            // 检查优先通道是否支持该国家/地区前缀
            if (smsChannelConfig.isChannelSupportPrefix(preferredChannel, prefix)) {
                log.info("使用前缀[{}]的优先通道: {} ({})", prefix,
                        preferredChannel, smsChannelConfig.getChannelName(preferredChannel));
                return preferredChannel;
            } else {
                log.warn("优先通道 {} ({}) 不支持国家/地区前缀 {}, 将尝试其他通道",
                        preferredChannel, smsChannelConfig.getChannelName(preferredChannel), prefix);
            }
        }

        // 获取支持该国家/地区前缀的所有可用通道
        java.util.List<Integer> supportedChannels = smsChannelConfig.getSupportedChannelsForPrefix(prefix);
        if (!supportedChannels.isEmpty()) {
            log.info("号码前缀[{}]支持的通道: {}", prefix, supportedChannels);
            // 如果有支持的通道，使用第一个
            return supportedChannels.get(0);
        } else {
            log.warn("没有找到支持国家/地区前缀 {} 的通道", prefix);
        }

        // 查找最近一次发送记录
        LambdaQueryWrapper<SmsChannelHistory> query = new LambdaQueryWrapper<>();
        query.eq(SmsChannelHistory::getPhone, phone)
             .orderByDesc(SmsChannelHistory::getSendTime)
             .last("LIMIT 1");

        SmsChannelHistory lastHistory = smsChannelHistoryMapper.selectOne(query);

        if (lastHistory == null) {
            // 如果没有历史记录，使用配置的默认通道（如果可用）
            int defaultChannel = smsChannelConfig.getDefaultChannel();
            if (smsChannelConfig.isChannelEnabled(defaultChannel)) {
                return defaultChannel;
            } else {
                // 默认通道不可用，使用第一个可用通道
                return availableChannels.get(0);
            }
        }

        // 如果上次发送在切换时间窗口之外，使用默认通道
        if (ChronoUnit.MINUTES.between(lastHistory.getSendTime(), LocalDateTime.now()) >= smsChannelConfig.getSwitchWindowMinutes()) {
            int defaultChannel = smsChannelConfig.getDefaultChannel();
            if (smsChannelConfig.isChannelEnabled(defaultChannel)) {
                return defaultChannel;
            } else {
                // 默认通道不可用，使用第一个可用通道
                return availableChannels.get(0);
            }
        }

        // 如果上次发送失败，切换到下一个可用通道
        if (!lastHistory.getSuccess()) {
            int currentChannel = lastHistory.getChannelType();

            // 找到下一个可用通道
            for (int i = 0; i < availableChannels.size(); i++) {
                if (availableChannels.get(i).equals(currentChannel)) {
                    // 找到当前通道，切换到下一个
                    int nextIndex = (i + 1) % availableChannels.size();
                    int nextChannel = availableChannels.get(nextIndex);
                    log.info("上次发送失败，从通道{}切换到通道{}",
                            smsChannelConfig.getChannelName(currentChannel),
                            smsChannelConfig.getChannelName(nextChannel));
                    return nextChannel;
                }
            }

            // 如果没找到当前通道（可能被禁用了），使用第一个可用通道
            return availableChannels.get(0);
        }

        // 如果上次发送成功，继续使用同一通道（如果仍然可用）
        int lastChannel = lastHistory.getChannelType();
        if (smsChannelConfig.isChannelEnabled(lastChannel)) {
            return lastChannel;
        } else {
            // 上次使用的通道已被禁用，使用第一个可用通道
            return availableChannels.get(0);
        }
    }
}
