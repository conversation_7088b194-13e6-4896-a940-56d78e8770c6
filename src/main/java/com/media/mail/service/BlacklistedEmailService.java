package com.media.mail.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.media.mail.entity.BlacklistedEmail;
import com.media.mail.mapper.BlacklistedEmailMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 黑名单邮箱服务
 */
@Service
public class BlacklistedEmailService extends ServiceImpl<BlacklistedEmailMapper, BlacklistedEmail> {

    private static final Logger logger = LoggerFactory.getLogger(BlacklistedEmailService.class);

    /**
     * 添加邮箱到黑名单
     * @param email 邮箱地址
     * @param reason 原因
     * @param permanent 是否永久拉黑
     * @return 是否添加成功
     */
    public boolean addToBlacklist(String email, String reason, boolean permanent) {
        try {
            // 检查是否已存在
            LambdaQueryWrapper<BlacklistedEmail> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BlacklistedEmail::getEmail, email);
            BlacklistedEmail existingRecord = getOne(queryWrapper, false);

            if (existingRecord != null) {
                // 更新现有记录
                existingRecord.setReason(reason);
                existingRecord.setPermanent(permanent);
                existingRecord.setBounceCount(existingRecord.getBounceCount() + 1);

                if (!permanent) {
                    // 设置默认过期时间为30天后
                    Date expiresAt = new Date(System.currentTimeMillis() + 30L * 24 * 60 * 60 * 1000);
                    existingRecord.setExpiresAt(expiresAt);
                }

                return updateById(existingRecord);
            } else {
                // 创建新记录
                BlacklistedEmail blacklistedEmail = BlacklistedEmail.builder()
                        .email(email)
                        .reason(reason)
                        .permanent(permanent)
                        .createdAt(new Date())
                        .bounceCount(1)
                        .createdBy("system")
                        .build();

                if (!permanent) {
                    // 设置默认过期时间为30天后
                    Date expiresAt = new Date(System.currentTimeMillis() + 30L * 24 * 60 * 60 * 1000);
                    blacklistedEmail.setExpiresAt(expiresAt);
                }

                return save(blacklistedEmail);
            }
        } catch (Exception e) {
            logger.error("Failed to add email to blacklist: {}", email, e);
            return false;
        }
    }

    /**
     * 添加域名到黑名单
     * @param domain 域名
     * @param reason 原因
     * @return 是否添加成功
     */
    public boolean addDomainToBlacklist(String domain, String reason) {
        // 将域名格式化为邮箱格式，使用domain作为邮箱地址
        String email = "blacklist@" + domain;
        return addToBlacklist(email, "Domain: " + domain + ", Reason: " + reason, true);
    }

    /**
     * 检查邮箱是否在黑名单中
     * @param email 邮箱地址
     * @return 是否在黑名单中
     */
    public boolean isBlacklisted(String email) {
        try {
            LambdaQueryWrapper<BlacklistedEmail> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BlacklistedEmail::getEmail, email);
            BlacklistedEmail blacklistedEmail = getOne(queryWrapper, false);

            if (blacklistedEmail == null) {
                return false;
            }

            // 检查是否过期
            return !blacklistedEmail.isExpired();
        } catch (Exception e) {
            logger.error("Failed to check blacklist for email: {}", email, e);
            return false;
        }
    }

    /**
     * 检查域名是否在黑名单中
     * @param domain 域名
     * @return 是否在黑名单中
     */
    public boolean isDomainBlacklisted(String domain) {
        // 将域名格式化为邮箱格式进行查询
        String email = "blacklist@" + domain;
        return isBlacklisted(email);
    }
}
