package com.media.mail.service;

import com.media.mail.bean.SendSmsTemplateRequest;
import com.media.mail.bean.SendSnsRequest;
import com.media.mail.config.SmsChannelConfig;
import com.media.mail.subsms.CnSmsHttpClient;
import com.media.mail.subsms.InterSmsHttpClient;
import com.media.mail.subsms.RunxinSmsHttpClient;
import com.media.mail.subsms.NxcloudSmsHttpClient;
import com.media.mail.subsms.ZhenyiSmsHttpClient;
import io.micrometer.common.util.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Map;


@Slf4j
@Service
@RequiredArgsConstructor
public class PhoneSnsService {

    private final SmsChannelHistoryService smsChannelHistoryService;
    private final SmsChannelConfig smsChannelConfig;

    @Value("${SYSTEM_SNS_SEND_FLAG:true}")
    private boolean snsSendEnabled;

    @Value("${inter.sms.channel.type:1}")
    private Integer interSmsChannelType;

    @Autowired
    InterSmsHttpClient interSmsHttpClient;

    @Autowired
    CnSmsHttpClient cnSmsHttpClient;

    @Autowired
    RunxinSmsHttpClient runxinSmsHttpClient;

    @Autowired
    NxcloudSmsHttpClient nxcloudSmsHttpClient;

    @Autowired
    ZhenyiSmsHttpClient zhenyiSmsHttpClient;

    //判断是国内还是国际
    public void sendSns(SendSnsRequest request) {
        if (!snsSendEnabled) {
            log.info("短信发送功能已禁用");
            return;
        }

        if(StringUtils.isNotEmpty(request.getPhonePrefix())
                && StringUtils.isNotEmpty(request.getPhone())
                && StringUtils.isNotEmpty(request.getContent())){
            if("+86".equalsIgnoreCase(request.getPhonePrefix())){
                log.info("send sms cn : {}", request.getPhone());
                cnSmsHttpClient.sendByContent(request.getPhone(), request.getContent());
            }else {
                log.info("send sms inter : {}-{}", request.getPhonePrefix(), request.getPhone());
                interSmsHttpClient.sendByContent(request.getPhonePrefix() + request.getPhone(), request.getContent());
            }
        }
    }

    /**
     * 使用模板发送短信
     * @param request 模板短信请求
     * @return 是否发送成功
     */
    public boolean sendSmsTemplate(SendSmsTemplateRequest request) {
        if (!snsSendEnabled) {
            log.info("短信发送功能已禁用");
            return false;
        }

        if(StringUtils.isNotEmpty(request.getPhonePrefix())
                && StringUtils.isNotEmpty(request.getPhone())
                && request.getVariables() != null) {

            boolean success = false;

            if("+86".equalsIgnoreCase(request.getPhonePrefix())) {
                // 国内短信
                log.info("发送国内模板短信: {}, 使用{}模板", request.getPhone(),
                        request.isUseEnglishTemplate() ? "英文" : "中文");

                if (request.isUseEnglishTemplate()) {
                    success = cnSmsHttpClient.sendEnglishSms(request.getPhone(), request.getVariables());
                } else {
                    success = cnSmsHttpClient.sendChineseSms(request.getPhone(), request.getVariables());
                }
            } else {
                // 国际短信
                String fullPhone = request.getPhonePrefix() + request.getPhone();
                log.info("发送国际模板短信: {}, 使用{}模板", fullPhone,
                        request.isUseEnglishTemplate() ? "英文" : "中文");

                // 根据历史记录决定使用哪个通道，并考虑通道对国家/地区的支持情况
                int suggestedChannel = smsChannelHistoryService.suggestChannel(fullPhone, request.getPhonePrefix());
                String channelName = smsChannelConfig.getChannelName(suggestedChannel);
                log.info("对号码 {} 建议使用通道: {} ({})", fullPhone, suggestedChannel, channelName);

                // 检查建议的通道是否支持该国家/地区前缀
                if (!smsChannelConfig.isChannelSupportPrefix(suggestedChannel, request.getPhonePrefix())) {
                    log.warn("建议的通道 {} ({}) 不支持国家/地区前缀 {}, 将尝试获取支持的通道",
                            suggestedChannel, channelName, request.getPhonePrefix());

                    // 获取支持该国家/地区前缀的所有可用通道
                    java.util.List<Integer> supportedChannels = smsChannelConfig.getSupportedChannelsForPrefix(request.getPhonePrefix());
                    if (!supportedChannels.isEmpty()) {
                        suggestedChannel = supportedChannels.get(0);
                        channelName = smsChannelConfig.getChannelName(suggestedChannel);
                        log.info("切换到支持前缀 {} 的通道: {} ({})", request.getPhonePrefix(), suggestedChannel, channelName);
                    } else {
                        log.warn("没有找到支持国家/地区前缀 {} 的通道，将继续尝试原通道", request.getPhonePrefix());
                    }
                }

                success = sendBySuggestedChannel(fullPhone, request.getVariables(), suggestedChannel, request.isUseEnglishTemplate());

                // 记录发送历史
                smsChannelHistoryService.recordSmsAttempt(fullPhone, request.getPhonePrefix(), suggestedChannel, success);
            }
            return success;
        }

        log.warn("短信请求参数不完整");
        return false;
    }

    /**
     * 根据建议的通道发送短信
     * @param fullPhone 完整的手机号（包含前缀）
     * @param variables 模板变量
     * @param channelId 通道ID
     * @param useEnglishTemplate 是否使用英文模板
     * @return 是否发送成功
     */
    private boolean sendBySuggestedChannel(String fullPhone, Map<String, String> variables, int channelId, boolean useEnglishTemplate) {
        try {
            switch (channelId) {
                case 1:
                    // Submail通道，总是使用英文模板
                    log.info("使用Submail通道发送短信到: {}", fullPhone);
                    return interSmsHttpClient.sendSms(fullPhone, variables, true);

                case 2:
                    // 拉力通道
                    log.info("使用拉力通道发送短信到: {}", fullPhone);
                    return interSmsHttpClient.sendLaLiSms(fullPhone, variables);

                case 3:
                    // 润信通道
                    log.info("使用润信通道发送短信到: {}", fullPhone);
                    String templateType = useEnglishTemplate ? "english" : "chinese";
                    return runxinSmsHttpClient.sendTemplateSms(fullPhone, variables, templateType);

                case 4:
                    // 牛信通道
                    log.info("使用牛信通道发送短信到: {}", fullPhone);
                    String nxTemplateType = useEnglishTemplate ? "english" : "chinese";
                    return nxcloudSmsHttpClient.sendTemplateSms(fullPhone, variables, nxTemplateType);

                case 5:
                    // 振亿通道
                    log.info("使用振亿通道发送短信到: {}", fullPhone);
                    String zyTemplateType = useEnglishTemplate ? "english" : "chinese";
                    return zhenyiSmsHttpClient.sendTemplateSms(fullPhone, variables, zyTemplateType);

                default:
                    log.warn("未知的短信通道ID: {}, 使用默认通道", channelId);
                    // 使用默认通道（Submail）
                    return interSmsHttpClient.sendSms(fullPhone, variables, true);
            }
        } catch (Exception e) {
            log.error("使用通道{}发送短信失败: {}", smsChannelConfig.getChannelName(channelId), fullPhone, e);
            return false;
        }
    }
}
