package com.media.mail.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class SubmailNotificationDTO {
    private String events;
    private String email;

    @JsonProperty("message_id")
    private String messageId;

    private String app;

    @JsonProperty("send_id")
    private String sendId;

    private String tag;
    private String reason;
    private String ip;
    private String agent;
    private String platform;
    private String device;

    @JsonProperty("country_code")
    private String countryCode;

    private String country;
    private String province;
    private String city;
    private String latitude;
    private String longitude;
    private Long timestamp;
    private String token;
    private String signature;
    private String url;

    @JsonProperty("new_email")
    private String newEmail;

    @JsonProperty("mail_to")
    private String mailTo;

    @JsonProperty("mail_from")
    private String mailFrom;

    private String subject;
    private String content;
}
