package com.media.mail.metrics;

import com.media.mail.entity.SmsSendStats;
import com.media.mail.service.SmsSendStatsService;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * 短信发送指标监控
 */
@Component
public class SmsMetrics {

    private static final Logger logger = LoggerFactory.getLogger(SmsMetrics.class);

    @Autowired
    private MeterRegistry meterRegistry;

    @Autowired
    private SmsSendStatsService smsSendStatsService;

    private Timer smsSendTimer;

    @PostConstruct
    public void init() {
        smsSendTimer = Timer.builder("sms.send.time")
                .description("短信发送处理时间")
                .register(meterRegistry);

        // 初始化一些基础计数器
        Counter.builder("sms.sent.total")
                .description("发送的短信总数")
                .register(meterRegistry);

        Counter.builder("sms.validation.failed")
                .description("短信验证失败次数")
                .register(meterRegistry);

        Counter.builder("sms.blacklisted")
                .description("被拦截的黑名单手机号数量")
                .register(meterRegistry);
    }

    /**
     * 记录短信发送
     * @param provider 短信提供商
     * @param type 短信类型
     * @param recipient 接收者手机号
     * @param countryCode 国家/地区代码
     * @param success 是否发送成功
     * @param processingTime 处理时间（毫秒）
     * @param errorMessage 错误信息
     */
    public void recordSmsSent(String provider, String type, String recipient,
                             String countryCode, boolean success,
                             long processingTime, String errorMessage) {
        try {
            // 记录处理时间
            smsSendTimer.record(processingTime, TimeUnit.MILLISECONDS);

            // 记录总发送数
            Counter.builder("sms.sent.total")
                    .tag("provider", provider)
                    .tag("type", type)
                    .tag("success", String.valueOf(success))
                    .description("发送的短信总数")
                    .register(meterRegistry)
                    .increment();

            // 如果失败，记录失败计数
            if (!success) {
                Counter.builder("sms.send.failed")
                        .tag("provider", provider)
                        .tag("type", type)
                        .description("短信发送失败次数")
                        .register(meterRegistry)
                        .increment();
            }

            // 保存到数据库
            SmsSendStats stats = SmsSendStats.builder()
                    .provider(provider)
                    .smsType(type)
                    .status(success ? "SUCCESS" : "FAILED")
                    .recipient(recipient)
                    .countryCode(countryCode)
                    .sendTime(new Date())
                    .processingTime(processingTime)
                    .errorMessage(errorMessage)
                    .deleted(0)
                    .createdAt(new Date())
                    .updatedAt(new Date())
                    .build();

            smsSendStatsService.recordSmsSent(stats);

        } catch (Exception e) {
            logger.error("记录短信发送指标失败", e);
        }
    }

    /**
     * 记录手机号验证失败
     * @param reason 失败原因
     */
    public void recordValidationFailed(String reason) {
        try {
            Counter.builder("sms.validation.failed")
                    .tag("reason", reason)
                    .description("短信验证失败次数")
                    .register(meterRegistry)
                    .increment();
        } catch (Exception e) {
            logger.error("记录手机号验证失败指标失败", e);
        }
    }

    /**
     * 记录黑名单拦截
     * @param countryCode 国家/地区代码
     */
    public void recordBlacklisted(String countryCode) {
        try {
            Counter.builder("sms.blacklisted")
                    .tag("country_code", countryCode)
                    .description("被拦截的黑名单手机号数量")
                    .register(meterRegistry)
                    .increment();
        } catch (Exception e) {
            logger.error("记录黑名单拦截指标失败", e);
        }
    }
}
