package com.media.mail.metrics;

import com.media.mail.entity.EmailSendStats;
import com.media.mail.service.EmailSendStatsService;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 邮件发送指标收集器 - 使用Prometheus和数据库存储
 */
@Component
public class EmailMetrics {

    private static final Logger logger = LoggerFactory.getLogger(EmailMetrics.class);

    private final MeterRegistry meterRegistry;

    // 硬退信计数器
    private final Map<String, AtomicInteger> bounceCounters = new ConcurrentHashMap<>();

    // 发送总数计数器
    private final Map<String, AtomicInteger> sendCounters = new ConcurrentHashMap<>();

    @Autowired
    private EmailSendStatsService emailSendStatsService;

    @Autowired
    public EmailMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;

        // 为每个提供商注册硬退信率指标
        registerBounceRateGauge("aws-ses");
        registerBounceRateGauge("submail");
    }

    /**
     * 注册硬退信率指标
     * @param provider 邮件服务提供商
     */
    private void registerBounceRateGauge(String provider) {
        Gauge.builder("email.bounce.rate", () -> calculateBounceRate(provider))
                .tag("provider", provider)
                .description("Email hard bounce rate")
                .register(meterRegistry);
    }

    /**
     * 记录邮件发送结果
     * @param provider 邮件服务提供商
     * @param type 邮件类型（普通/HTML/模板）
     * @param success 是否发送成功
     * @param duration 发送耗时(毫秒)
     * @param recipient 收件人邮箱地址
     */
    public void recordEmailSent(String provider, String type, boolean success, long duration, String recipient) {
        // 计数器：总发送次数
        Counter.builder("email.sent.total")
                .tag("provider", provider)
                .tag("type", type)
                .description("Total number of emails sent")
                .register(meterRegistry)
                .increment();

        // 计数器：成功/失败次数
        Counter.builder("email.sent." + (success ? "success" : "failure"))
                .tag("provider", provider)
                .tag("type", type)
                .description(success ? "Number of emails sent successfully" : "Number of emails failed to send")
                .register(meterRegistry)
                .increment();

        // 计时器：发送耗时
        Timer.builder("email.sent.duration")
                .tag("provider", provider)
                .tag("type", type)
                .tag("success", String.valueOf(success))
                .description("Time taken to send an email")
                .register(meterRegistry)
                .record(duration, TimeUnit.MILLISECONDS);

        // 将数据保存到数据库
        try {
            EmailSendStats stats = EmailSendStats.builder()
                    .provider(provider)
                    .emailType(type)
                    .status(success ? "SUCCESS" : "FAILURE")
                    .recipient(recipient) // 使用传入的recipient参数
                    .processingTime(duration)
                    .sendTime(new Date())
                    .messageId(UUID.randomUUID().toString())
                    .createdAt(new Date())
                    .deleted(0)
                    .build();

            emailSendStatsService.recordEmailSendStats(stats);
        } catch (Exception e) {
            logger.error("Failed to save email send stats to database", e);
        }
    }

    /**
     * 记录故障转移事件
     * @param fromProvider 原提供商
     * @param toProvider 目标提供商
     */
    public void recordFailover(String fromProvider, String toProvider) {
        Counter.builder("email.failover")
                .tag("from", fromProvider)
                .tag("to", toProvider)
                .description("Number of failovers between email providers")
                .register(meterRegistry)
                .increment();
    }

    /**
     * 记录邮箱验证结果
     * @param valid 是否有效
     * @param reason 无效原因
     * @param duration 验证耗时(毫秒)
     */
    public void recordEmailValidation(boolean valid, String reason, long duration) {
        // 计数器：验证次数
        Counter.builder("email.validation.total")
                .tag("valid", String.valueOf(valid))
                .tag("reason", reason)
                .description("Total number of email validations")
                .register(meterRegistry)
                .increment();

        // 计时器：验证耗时
        Timer.builder("email.validation.duration")
                .tag("valid", String.valueOf(valid))
                .description("Time taken to validate an email")
                .register(meterRegistry)
                .record(duration, TimeUnit.MILLISECONDS);
    }

    /**
     * 记录硬退信
     * @param provider 邮件服务提供商
     * @param recipient 接收者邮箱
     * @param reason 退信原因
     */
    public void recordBounce(String provider, String recipient, String reason) {
        // 增加硬退信计数
        bounceCounters.computeIfAbsent(provider, k -> new AtomicInteger(0)).incrementAndGet();

        // 增加发送总数计数
        sendCounters.computeIfAbsent(provider, k -> new AtomicInteger(0)).incrementAndGet();

        // 记录硬退信事件
        Counter.builder("email.bounce")
                .tag("provider", provider)
                .description("Number of email hard bounces")
                .register(meterRegistry)
                .increment();

        // 将退信数据保存到数据库
        try {
            // 更新邮件发送统计信息
            if (recipient != null && !recipient.isEmpty()) {
                EmailSendStats stats = EmailSendStats.builder()
                        .provider(provider)
                        .status("BOUNCE")
                        .recipient(recipient)
                        .errorMessage(reason)
                        .sendTime(new Date())
                        .messageId(UUID.randomUUID().toString())
                        .createdAt(new Date())
                        .deleted(0)
                        .build();

                emailSendStatsService.recordEmailSendStats(stats);
                logger.info("Recorded bounce event for recipient: {} (provider: {})", recipient, provider);
            }
        } catch (Exception e) {
            logger.error("Failed to save bounce event to database", e);
        }
    }

    /**
     * 记录硬退信 (兼容旧版本)
     * @param provider 邮件服务提供商
     */
    public void recordBounce(String provider) {
        recordBounce(provider, null, "Unknown reason");
    }

    /**
     * 计算硬退信率
     * @param provider 邮件服务提供商
     * @return 硬退信率(0-1之间的小数)
     */
    private double calculateBounceRate(String provider) {
        AtomicInteger bounceCount = bounceCounters.get(provider);
        AtomicInteger sendCount = sendCounters.get(provider);

        if (bounceCount == null || sendCount == null || sendCount.get() == 0) {
            return 0.0;
        }

        return (double) bounceCount.get() / sendCount.get();
    }
}
