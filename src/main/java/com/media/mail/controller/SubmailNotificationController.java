package com.media.mail.controller;

import com.media.mail.dto.SubmailNotificationDTO;
import com.media.mail.service.VerifiedEmailService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/submail/notification")
@RequiredArgsConstructor
public class SubmailNotificationController {

    private final VerifiedEmailService verifiedEmailService;

    @PostMapping
    public void handleNotification(@RequestBody SubmailNotificationDTO notification) {
        log.info("Received Submail notification: {}", notification.getEvents());

        switch (notification.getEvents()) {
            case "delivered":
                handleDelivered(notification);
                break;
            case "bounced":
            case "dropped":
                handleBounceOrDrop(notification);
                break;
            case "opened":
                handleOpened(notification);
                break;
            case "clicked":
                handleClicked(notification);
                break;
            case "marked_as_spam":
            case "report_as_spam":
                handleSpam(notification);
                break;
            case "rejected":
                handleRejected(notification);
                break;
            case "unsubscribe":
                handleUnsubscribe(notification);
                break;
            case "subscribe":
                handleSubscribe(notification);
                break;
            case "change_subscribe":
                handleChangeSubscribe(notification);
                break;
            case "receivers":
                handleReceivers(notification);
                break;
            case "request":
                handleRequest(notification);
                break;
            default:
                log.warn("Unknown event type: {}", notification.getEvents());
        }
    }

    private void handleDelivered(SubmailNotificationDTO notification) {
        log.info("Email delivered to {}", notification.getEmail());
        verifiedEmailService.addVerifiedEmail(notification.getEmail(), "submail_delivery");
    }

    private void handleBounceOrDrop(SubmailNotificationDTO notification) {
        log.warn("Email bounce/drop for {}: {}", notification.getEmail(), notification.getReason());
        // TODO: Add to blacklist or handle accordingly
    }

    private void handleOpened(SubmailNotificationDTO notification) {
        log.info("Email opened by {} from {} using {}",
            notification.getEmail(),
            notification.getCity(),
            notification.getAgent());
    }

    private void handleClicked(SubmailNotificationDTO notification) {
        log.info("Link clicked by {} - URL: {}", notification.getEmail(), notification.getUrl());
    }

    private void handleSpam(SubmailNotificationDTO notification) {
        log.warn("Email marked as spam by {}", notification.getEmail());
        // TODO: Add to blacklist or handle accordingly
    }

    private void handleRejected(SubmailNotificationDTO notification) {
        log.warn("Email rejected for {}", notification.getEmail());
        // TODO: Add to blacklist or handle accordingly
    }

    private void handleUnsubscribe(SubmailNotificationDTO notification) {
        log.info("Unsubscribe request from {}", notification.getEmail());
        // TODO: Handle unsubscribe request
    }

    private void handleSubscribe(SubmailNotificationDTO notification) {
        log.info("Subscribe request from {}", notification.getEmail());
        // TODO: Handle subscribe request
    }

    private void handleChangeSubscribe(SubmailNotificationDTO notification) {
        log.info("Email change request from {} to {}",
            notification.getEmail(),
            notification.getNewEmail());
        // TODO: Handle email change request
    }

    private void handleReceivers(SubmailNotificationDTO notification) {
        log.info("Received email from {} to {}: {}",
            notification.getMailFrom(),
            notification.getMailTo(),
            notification.getSubject());
    }

    private void handleRequest(SubmailNotificationDTO notification) {
        log.info("Send request for {}", notification.getEmail());
    }
}
