package com.media.mail.controller;

import com.media.mail.config.SmsChannelConfig;
import com.media.mail.service.SmsChannelHistoryService;
import com.media.mail.subsms.CnSmsHttpClient;
import com.media.mail.subsms.InterSmsHttpClient;
import com.media.mail.subsms.RunxinSmsHttpClient;
import com.media.mail.subsms.NxcloudSmsHttpClient;
import com.media.mail.subsms.ZhenyiSmsHttpClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 短信渠道管理控制器
 * 提供渠道状态查询、测试和管理功能
 */
@Slf4j
@RestController
@RequestMapping("/sms/channel")
@RequiredArgsConstructor
public class SmsChannelController {

    private final SmsChannelConfig smsChannelConfig;
    private final SmsChannelHistoryService smsChannelHistoryService;
    private final CnSmsHttpClient cnSmsHttpClient;
    private final InterSmsHttpClient interSmsHttpClient;
    private final RunxinSmsHttpClient runxinSmsHttpClient;
    private final NxcloudSmsHttpClient nxcloudSmsHttpClient;
    private final ZhenyiSmsHttpClient zhenyiSmsHttpClient;

    /**
     * 获取所有短信渠道状态
     * @return 渠道状态信息
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getChannelStatus() {
        Map<String, Object> response = new HashMap<>();

        // 渠道状态
        Map<String, Object> channels = new HashMap<>();
        channels.put("submail", Map.of(
                "id", 1,
                "name", "Submail",
                "enabled", smsChannelConfig.isSubmailEnabled(),
                "description", "国际短信主要渠道"
        ));
        channels.put("lali", Map.of(
                "id", 2,
                "name", "拉力",
                "enabled", smsChannelConfig.isLaliEnabled(),
                "description", "国际短信备用渠道"
        ));
        channels.put("runxin", Map.of(
                "id", 3,
                "name", "润信",
                "enabled", smsChannelConfig.isRunxinEnabled(),
                "description", "批量短信渠道",
                "configStatus", runxinSmsHttpClient.getConfigStatus()
        ));
        channels.put("nxcloud", Map.of(
                "id", 4,
                "name", "牛信",
                "enabled", smsChannelConfig.isNxcloudEnabled(),
                "description", "牛信短信渠道",
                "configStatus", nxcloudSmsHttpClient.getConfigStatus()
        ));
        channels.put("zhenyi", Map.of(
                "id", 5,
                "name", "振亿",
                "enabled", smsChannelConfig.isZhenyiEnabled(),
                "description", "振亿短信渠道",
                "configStatus", zhenyiSmsHttpClient.getConfigStatus()
        ));

        // 可用渠道列表
        List<Integer> availableChannels = smsChannelConfig.getAvailableChannels();

        // 配置信息
        Map<String, Object> config = new HashMap<>();
        config.put("defaultChannel", smsChannelConfig.getDefaultChannel());
        config.put("defaultChannelName", smsChannelConfig.getChannelName(smsChannelConfig.getDefaultChannel()));
        config.put("switchWindowMinutes", smsChannelConfig.getSwitchWindowMinutes());
        config.put("prefixChannelMap", smsChannelConfig.getPrefixChannelMap());

        response.put("channels", channels);
        response.put("availableChannels", availableChannels);
        response.put("config", config);
        response.put("timestamp", System.currentTimeMillis());

        return ResponseEntity.ok(response);
    }

    /**
     * 获取指定号码的推荐渠道
     * @param phone 手机号
     * @param prefix 号码前缀
     * @return 推荐渠道信息
     */
    @GetMapping("/suggest")
    public ResponseEntity<Map<String, Object>> getSuggestedChannel(
            @RequestParam String phone,
            @RequestParam(required = false, defaultValue = "+86") String prefix) {

        Map<String, Object> response = new HashMap<>();

        try {
            Integer suggestedChannel = smsChannelHistoryService.suggestChannel(phone, prefix);
            String channelName = smsChannelConfig.getChannelName(suggestedChannel);
            boolean isEnabled = smsChannelConfig.isChannelEnabled(suggestedChannel);

            response.put("success", true);
            response.put("phone", phone);
            response.put("prefix", prefix);
            response.put("suggestedChannel", suggestedChannel);
            response.put("channelName", channelName);
            response.put("isEnabled", isEnabled);

        } catch (Exception e) {
            log.error("获取推荐渠道失败", e);
            response.put("success", false);
            response.put("message", "获取推荐渠道失败: " + e.getMessage());
        }

        return ResponseEntity.ok(response);
    }

    /**
     * 测试润信渠道配置
     * @return 测试结果
     */
    @PostMapping("/test/runxin")
    public ResponseEntity<Map<String, Object>> testRunxinChannel() {
        Map<String, Object> response = new HashMap<>();

        try {
            // 检查配置
            boolean configValid = runxinSmsHttpClient.isConfigValid();
            String configStatus = runxinSmsHttpClient.getConfigStatus();

            response.put("configValid", configValid);
            response.put("configStatus", configStatus);
            response.put("enabled", smsChannelConfig.isRunxinEnabled());

            if (!configValid) {
                response.put("success", false);
                response.put("message", "润信配置不完整，无法进行测试");
                return ResponseEntity.ok(response);
            }

            if (!smsChannelConfig.isRunxinEnabled()) {
                response.put("success", false);
                response.put("message", "润信渠道未启用");
                return ResponseEntity.ok(response);
            }

            response.put("success", true);
            response.put("message", "润信渠道配置正常，可以发送短信");

        } catch (Exception e) {
            log.error("测试润信渠道失败", e);
            response.put("success", false);
            response.put("message", "测试失败: " + e.getMessage());
        }

        return ResponseEntity.ok(response);
    }

    /**
     * 测试润信发送短信
     * @param phone 手机号
     * @param message 短信内容（可选，使用默认测试内容）
     * @return 发送结果
     */
    @PostMapping("/test/runxin/send")
    public ResponseEntity<Map<String, Object>> testRunxinSend(
            @RequestParam String phone,
            @RequestParam(required = false) String message) {

        Map<String, Object> response = new HashMap<>();

        try {
            // 检查润信配置
            if (!runxinSmsHttpClient.isConfigValid()) {
                response.put("success", false);
                response.put("message", "润信配置不完整：" + runxinSmsHttpClient.getConfigStatus());
                return ResponseEntity.ok(response);
            }

            if (!smsChannelConfig.isRunxinEnabled()) {
                response.put("success", false);
                response.put("message", "润信渠道未启用");
                return ResponseEntity.ok(response);
            }

            log.info("开始测试润信发送短信到: {}", phone);

            // 直接调用润信发送
            Map<String, String> messageMap = new HashMap<>();
            messageMap.put("phone", phone);
            messageMap.put("code", "123456");
            boolean success = runxinSmsHttpClient.sendTemplateSms(phone, messageMap,"english");

            response.put("success", success);
            response.put("phone", phone);
            response.put("message", success ? "润信短信发送成功" : "润信短信发送失败");
            response.put("content", message);
            response.put("channel", "润信");
            response.put("timestamp", System.currentTimeMillis());

            log.info("润信短信发送结果: {}, 手机号: {}", success, phone);

        } catch (Exception e) {
            log.error("测试润信发送失败", e);
            response.put("success", false);
            response.put("message", "测试发送失败: " + e.getMessage());
        }

        return ResponseEntity.ok(response);
    }

    /**
     * 测试润信模板短信发送
     * @param phone 手机号
     * @param code 验证码（可选，默认123456）
     * @param time 有效时间（可选，默认5分钟）
     * @param templateType 模板类型：chinese或english（可选，默认chinese）
     * @return 发送结果
     */
    @PostMapping("/test/runxin/template")
    public ResponseEntity<Map<String, Object>> testRunxinTemplate(
            @RequestParam String phone,
            @RequestParam(required = false, defaultValue = "123456") String code,
            @RequestParam(required = false, defaultValue = "5") String time,
            @RequestParam(required = false, defaultValue = "english") String templateType) {

        Map<String, Object> response = new HashMap<>();

        try {
            // 检查润信配置
            if (!runxinSmsHttpClient.isConfigValid()) {
                response.put("success", false);
                response.put("message", "润信配置不完整：" + runxinSmsHttpClient.getConfigStatus());
                return ResponseEntity.ok(response);
            }

            if (!smsChannelConfig.isRunxinEnabled()) {
                response.put("success", false);
                response.put("message", "润信渠道未启用");
                return ResponseEntity.ok(response);
            }

            // 构建模板变量
            Map<String, String> variables = new HashMap<>();
            variables.put("code", code);
            variables.put("time", time);

            log.info("开始测试润信模板短信发送到: {}, 模板类型: {}", phone, templateType);

            // 直接调用润信模板发送
            boolean success = runxinSmsHttpClient.sendTemplateSms(phone, variables, templateType);

            response.put("success", success);
            response.put("phone", phone);
            response.put("message", success ? "润信模板短信发送成功" : "润信模板短信发送失败");
            response.put("variables", variables);
            response.put("templateType", templateType);
            response.put("channel", "润信");
            response.put("timestamp", System.currentTimeMillis());

            log.info("润信模板短信发送结果: {}, 手机号: {}, 模板: {}", success, phone, templateType);

        } catch (Exception e) {
            log.error("测试润信模板发送失败", e);
            response.put("success", false);
            response.put("message", "测试模板发送失败: " + e.getMessage());
        }

        return ResponseEntity.ok(response);
    }

    /**
     * 测试牛信渠道配置
     * @return 测试结果
     */
    @PostMapping("/test/nxcloud")
    public ResponseEntity<Map<String, Object>> testNxcloudChannel() {
        Map<String, Object> response = new HashMap<>();

        try {
            // 检查配置
            boolean configValid = nxcloudSmsHttpClient.isConfigValid();
            String configStatus = nxcloudSmsHttpClient.getConfigStatus();

            response.put("configValid", configValid);
            response.put("configStatus", configStatus);
            response.put("enabled", smsChannelConfig.isNxcloudEnabled());

            if (!configValid) {
                response.put("success", false);
                response.put("message", "牛信配置不完整，无法进行测试");
                return ResponseEntity.ok(response);
            }

            if (!smsChannelConfig.isNxcloudEnabled()) {
                response.put("success", false);
                response.put("message", "牛信渠道未启用");
                return ResponseEntity.ok(response);
            }

            response.put("success", true);
            response.put("message", "牛信渠道配置正常，可以发送短信");

        } catch (Exception e) {
            log.error("测试牛信渠道失败", e);
            response.put("success", false);
            response.put("message", "测试失败: " + e.getMessage());
        }

        return ResponseEntity.ok(response);
    }

    /**
     * 测试牛信模板短信发送
     * @param phone 手机号
     * @param code 验证码（可选，默认123456）
     * @param time 有效时间（可选，默认5分钟）
     * @param templateType 模板类型：chinese或english（可选，默认english）
     * @return 发送结果
     */
    @PostMapping("/test/nxcloud/template")
    public ResponseEntity<Map<String, Object>> testNxcloudTemplate(
            @RequestParam String phone,
            @RequestParam(required = false, defaultValue = "123456") String code,
            @RequestParam(required = false, defaultValue = "5") String time,
            @RequestParam(required = false, defaultValue = "english") String templateType) {

        Map<String, Object> response = new HashMap<>();

        try {
            // 检查牛信配置
            if (!nxcloudSmsHttpClient.isConfigValid()) {
                response.put("success", false);
                response.put("message", "牛信配置不完整：" + nxcloudSmsHttpClient.getConfigStatus());
                return ResponseEntity.ok(response);
            }

            if (!smsChannelConfig.isNxcloudEnabled()) {
                response.put("success", false);
                response.put("message", "牛信渠道未启用");
                return ResponseEntity.ok(response);
            }

            // 构建模板变量
            Map<String, String> variables = new HashMap<>();
            variables.put("code", code);
            variables.put("time", time);

            log.info("开始测试牛信模板短信发送到: {}, 模板类型: {}", phone, templateType);

            // 直接调用牛信模板发送
            boolean success = nxcloudSmsHttpClient.sendTemplateSms(phone, variables, templateType);

            response.put("success", success);
            response.put("phone", phone);
            response.put("message", success ? "牛信模板短信发送成功" : "牛信模板短信发送失败");
            response.put("variables", variables);
            response.put("templateType", templateType);
            response.put("channel", "牛信");
            response.put("timestamp", System.currentTimeMillis());

            log.info("牛信模板短信发送结果: {}, 手机号: {}, 模板: {}", success, phone, templateType);

        } catch (Exception e) {
            log.error("测试牛信模板发送失败", e);
            response.put("success", false);
            response.put("message", "测试模板发送失败: " + e.getMessage());
        }

        return ResponseEntity.ok(response);
    }

    /**
     * 获取渠道统计信息
     * @param days 统计天数，默认7天
     * @return 统计信息
     */
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getChannelStats(
            @RequestParam(required = false, defaultValue = "7") int days) {

        Map<String, Object> response = new HashMap<>();

        try {
            // 这里可以添加统计逻辑，比如各渠道的成功率、发送量等
            // 目前先返回基本信息
            response.put("success", true);
            response.put("days", days);
            response.put("message", "统计功能开发中");
            response.put("availableChannels", smsChannelConfig.getAvailableChannels().size());

        } catch (Exception e) {
            log.error("获取渠道统计失败", e);
            response.put("success", false);
            response.put("message", "获取统计信息失败: " + e.getMessage());
        }

        return ResponseEntity.ok(response);
    }

    /**
     * 测试振亿渠道配置
     * @return 测试结果
     */
    @PostMapping("/test/zhenyi")
    public ResponseEntity<Map<String, Object>> testZhenyiChannel() {
        Map<String, Object> response = new HashMap<>();

        try {
            // 检查配置
            boolean configValid = zhenyiSmsHttpClient.isConfigValid();
            String configStatus = zhenyiSmsHttpClient.getConfigStatus();

            response.put("configValid", configValid);
            response.put("configStatus", configStatus);
            response.put("enabled", smsChannelConfig.isZhenyiEnabled());

            if (!configValid) {
                response.put("success", false);
                response.put("message", "振亿配置不完整，无法进行测试");
                return ResponseEntity.ok(response);
            }

            if (!smsChannelConfig.isZhenyiEnabled()) {
                response.put("success", false);
                response.put("message", "振亿渠道未启用");
                return ResponseEntity.ok(response);
            }

            response.put("success", true);
            response.put("message", "振亿渠道配置正常，可以发送短信");

        } catch (Exception e) {
            log.error("测试振亿渠道失败", e);
            response.put("success", false);
            response.put("message", "测试失败: " + e.getMessage());
        }

        return ResponseEntity.ok(response);
    }

    /**
     * 测试振亿模板短信发送
     * @param phone 手机号
     * @param code 验证码（可选，默认123456）
     * @param time 有效时间（可选，默认5分钟）
     * @param templateType 模板类型：chinese或english（可选，默认chinese）
     * @return 发送结果
     */
    @PostMapping("/test/zhenyi/template")
    public ResponseEntity<Map<String, Object>> testZhenyiTemplate(
            @RequestParam String phone,
            @RequestParam(required = false, defaultValue = "123456") String code,
            @RequestParam(required = false, defaultValue = "5") String time,
            @RequestParam(required = false, defaultValue = "chinese") String templateType) {

        Map<String, Object> response = new HashMap<>();

        try {
            // 检查振亿配置
            if (!zhenyiSmsHttpClient.isConfigValid()) {
                response.put("success", false);
                response.put("message", "振亿配置不完整：" + zhenyiSmsHttpClient.getConfigStatus());
                return ResponseEntity.ok(response);
            }

            if (!smsChannelConfig.isZhenyiEnabled()) {
                response.put("success", false);
                response.put("message", "振亿渠道未启用");
                return ResponseEntity.ok(response);
            }

            // 构建模板变量
            Map<String, String> variables = new HashMap<>();
            variables.put("code", code);
            variables.put("time", time);

            log.info("开始测试振亿模板短信发送到: {}, 模板类型: {}", phone, templateType);

            // 直接调用振亿模板发送
            boolean success = zhenyiSmsHttpClient.sendTemplateSms(phone, variables, templateType);

            response.put("success", success);
            response.put("phone", phone);
            response.put("message", success ? "振亿模板短信发送成功" : "振亿模板短信发送失败");
            response.put("variables", variables);
            response.put("templateType", templateType);
            response.put("channel", "振亿");
            response.put("timestamp", System.currentTimeMillis());

            log.info("振亿模板短信发送结果: {}, 手机号: {}, 模板: {}", success, phone, templateType);

        } catch (Exception e) {
            log.error("测试振亿模板发送失败", e);
            response.put("success", false);
            response.put("message", "测试模板发送失败: " + e.getMessage());
        }

        return ResponseEntity.ok(response);
    }
}
