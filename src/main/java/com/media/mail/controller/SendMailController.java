package com.media.mail.controller;

import com.media.mail.bean.SendMailRequest;
import com.media.mail.bean.SendTemplateEmailRequest;
import com.media.mail.util.EmailSender;
import com.media.mail.util.EmailSenderFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/mail")
public class SendMailController {

    private static final Logger logger = LoggerFactory.getLogger(SendMailController.class);

    //测试邮件  alan_  testxme1_
    String[] testEmail = new String[]{"alan_", "testxme1_"};

    @Value(value = "${system.emailSend:true}")
    private Boolean isSendEmail;

    @Autowired
    private EmailSenderFactory emailSenderFactory;

    /**
     * 发送普通邮件
     * @param request 邮件请求
     * @return 响应结果
     */
    @PostMapping("/send")
    public ResponseEntity<Map<String, Object>> send(@RequestBody SendMailRequest request) {
        Map<String, Object> response = new HashMap<>();

        if(!isSendEmail){
            logger.info("config set do not send email");
            response.put("success", false);
            response.put("message", "Email sending is disabled by configuration");
            return ResponseEntity.ok(response);
        } else {
            new Thread(() -> {
                try {
                    boolean isTestEmail = false; //默认不是测试邮件
                    for (String email : testEmail) {
                        if (request.getTo().startsWith(email)) {
                            logger.info("normal test email: {}", request.getTo());
                            isTestEmail = true;
                        }
                    }
                    if (!isTestEmail) {
                        EmailSender emailSender = emailSenderFactory.getEmailSender();
                        emailSender.sendEmail(request);
                    }
                    //amazonSESSender.sendEmail(request.getTo(), request.getSubject(), request.getContent());
                    logger.info("Success to send email, request={}", request);
                } catch (Exception e) {
                    logger.error("Failed to send email, request={}, error={}", request, e.getMessage());
                }
            }).start();
        }

        response.put("success", true);
        response.put("message", "Email sent successfully");
        return ResponseEntity.ok(response);
    }

    /**
     * 发送HTML邮件
     * @param request 邮件请求
     * @return 响应结果
     */
    @PostMapping("/html/send")
    public ResponseEntity<Map<String, Object>> sendHtml(@RequestBody SendMailRequest request) {
        Map<String, Object> response = new HashMap<>();

        if(!isSendEmail){
            logger.info("config set do not send email");
            response.put("success", false);
            response.put("message", "Email sending is disabled by configuration");
            return ResponseEntity.ok(response);
        } else {
            new Thread(() -> {
                boolean isTestEmail = false; //默认不是测试邮件
                for (String email : testEmail) {
                    if (request.getTo().startsWith(email)) {
                        logger.info("html test email: {}", request.getTo());
                        isTestEmail = true;
                    }
                }
                if (!isTestEmail) {
                    EmailSender emailSender = emailSenderFactory.getEmailSender();
                    emailSender.sendHtmlEmail(request);
                }
            }).start();
        }

        response.put("success", true);
        response.put("message", "HTML email sent successfully");
        return ResponseEntity.ok(response);
    }

    /**
     * 发送模板邮件
     * @param request 模板邮件请求
     * @return 响应结果
     */
    @PostMapping("/template/send")
    public ResponseEntity<Map<String, Object>> sendTemplate(@RequestBody SendTemplateEmailRequest request) {
        Map<String, Object> response = new HashMap<>();

        if(!isSendEmail){
            logger.info("config set do not send email");
            response.put("success", false);
            response.put("message", "Email sending is disabled by configuration");
            return ResponseEntity.ok(response);
        } else {
            new Thread(() -> {
                boolean isTestEmail = false; //默认不是测试邮件
                for (String email : testEmail) {
                    if (request.getTo().startsWith(email)) {
                        logger.info("template test email: {}", request.getTo());
                        isTestEmail = true;
                    }
                }
                if (!isTestEmail) {
                    EmailSender emailSender = emailSenderFactory.getEmailSender();
                    emailSender.sendTemplateEmail(
                        request.getTo(),
                        request.getTemplateId(),
                        request.getVariables(),
                        request.getSubject()
                    );
                    logger.info("Success to send template email, request={}", request);
                }
            }).start();
        }

        response.put("success", true);
        response.put("message", "Template email sent successfully");
        return ResponseEntity.ok(response);
    }

}
