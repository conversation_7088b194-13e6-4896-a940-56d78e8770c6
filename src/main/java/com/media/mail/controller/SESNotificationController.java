package com.media.mail.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONArray;
import com.media.mail.metrics.EmailMetrics;
import com.media.mail.service.BouncedEmailService;
import com.media.mail.service.VerifiedEmailService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;
import java.io.IOException;

/**
 * AWS SES通知控制器
 * 用于接收AWS SES的退信、投诉等通知
 */
@RestController
@RequestMapping("/ses/notification")
public class SESNotificationController {

    private static final Logger logger = LoggerFactory.getLogger(SESNotificationController.class);

    @Autowired
    private EmailMetrics emailMetrics;

    @Autowired
    private BouncedEmailService bouncedEmailService;

    @Autowired
    private VerifiedEmailService verifiedEmailService;

    /**
     * 处理AWS SES通知 - HTTP端点
     * @param payload 通知内容
     * @return 处理结果
     */
    @PostMapping
    public ResponseEntity<Map<String, Object>> handleNotification(@RequestBody String payload) {
        Map<String, Object> response = new HashMap<>();

        try {
            processNotificationPayload(payload);

            response.put("success", true);
            response.put("message", "Notification processed successfully");
        } catch (Exception e) {
            logger.error("Failed to process SES notification", e);
            response.put("success", false);
            response.put("message", "Failed to process notification: " + e.getMessage());
        }

        return ResponseEntity.ok(response);
    }

    /**
     * 处理通知内容 - 可被HTTP端点或SQS服务调用
     * @param payload 通知内容
     * @throws IOException 处理异常
     */
    public void processNotificationPayload(String payload) throws IOException {
        try {
            logger.info("Received notification payload: {}", payload);
            JSONObject notification = JSON.parseObject(payload);

            // 检查是否是 SNS 通知
            if (notification.containsKey("Type")) {
                String notificationType = notification.getString("Type");

                if ("SubscriptionConfirmation".equals(notificationType)) {
                    // 处理订阅确认
                    handleSubscriptionConfirmation(notification);
                    return;
                } else if ("Notification".equals(notificationType)) {
                    // 处理通过 SNS 转发的通知消息
                    handleSESNotification(notification);
                    return;
                } else {
                    logger.info("Received unknown SNS notification type: {}", notificationType);
                    return;
                }
            }

            // 如果不是 SNS 通知，尝试直接处理为 SES 事件通知
            if (notification.containsKey("eventType")) {
                String eventType = notification.getString("eventType");

                switch (eventType) {
                    case "Bounce":
                        handleBounce(notification);
                        break;
                    case "Complaint":
                        handleComplaint(notification);
                        break;
                    case "Delivery":
                        handleDelivery(notification);
                        break;
                    default:
                        logger.info("Received unknown SES event type: {}", eventType);
                }
            }
            // 处理使用 notificationType 的通知格式
            else if (notification.containsKey("notificationType")) {
                String notificationType = notification.getString("notificationType");

                switch (notificationType) {
                    case "Bounce":
                        handleBounce(notification);
                        break;
                    case "Complaint":
                        handleComplaint(notification);
                        break;
                    case "Delivery":
                        handleDelivery(notification);
                        break;
                    default:
                        logger.info("Received unknown SES notification type: {}", notificationType);
                }
            } else {
                logger.warn("Received notification with unknown format: neither SNS notification nor SES event");
            }
        } catch (Exception e) {
            logger.error("Failed to process notification payload", e);
            throw new IOException("Failed to process notification: " + e.getMessage(), e);
        }
    }

    /**
     * 处理订阅确认
     * @param notification 通知内容
     */
    private void handleSubscriptionConfirmation(JSONObject notification) {
        try {
            String subscribeURL = notification.getString("SubscribeURL");
            String token = notification.getString("Token");
            String topicArn = notification.getString("TopicArn");

            logger.info("Received SES subscription confirmation. TopicArn: {}, Token: {}", topicArn, token);
            logger.info("Automatically confirming subscription by visiting: {}", subscribeURL);

            // 自动访问订阅URL进行确认
            java.net.URL url = new java.net.URL(subscribeURL);
            java.net.HttpURLConnection connection = (java.net.HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");

            int responseCode = connection.getResponseCode();
            if (responseCode == 200) {
                logger.info("Successfully confirmed SES subscription");
            } else {
                logger.error("Failed to confirm SES subscription. Response code: {}", responseCode);
            }
        } catch (Exception e) {
            logger.error("Error confirming SES subscription", e);
        }
    }

    /**
     * 处理SES通知
     * @param notification 通知内容
     */
    private void handleSESNotification(JSONObject notification) {
        try {
            String message = notification.getString("Message");
            JSONObject messageObj = JSON.parseObject(message);
            String notificationType = messageObj.getString("notificationType");

            switch (notificationType) {
                case "Bounce":
                    handleBounce(messageObj);
                    break;
                case "Complaint":
                    handleComplaint(messageObj);
                    break;
                case "Delivery":
                    handleDelivery(messageObj);
                    break;
                default:
                    logger.info("Received unknown SES notification type: {}", notificationType);
            }
        } catch (Exception e) {
            logger.error("Error processing SES notification message", e);
        }
    }

    /**
     * 处理退信通知
     * @param message 通知内容
     */
    private void handleBounce(JSONObject message) {
        try {
            JSONObject bounce = message.getJSONObject("bounce");
            String bounceType = bounce.getString("bounceType");
            String bounceSubType = bounce.getString("bounceSubType");
            String timestamp = bounce.getString("timestamp");

            // 获取邮件发送相关信息
            JSONObject mail = message.getJSONObject("mail");
            String messageId = mail.getString("messageId");
            String source = mail.getString("source");
            String sourceArn = mail.getString("sourceArn");

            // 记录所有退信的收件人
            for (Object recipientObj : bounce.getJSONArray("bouncedRecipients")) {
                JSONObject recipient = (JSONObject) recipientObj;
                String email = recipient.getString("emailAddress");
                String action = recipient.getString("action");
                String status = recipient.getString("status");
                String diagnosticCode = recipient.getString("diagnosticCode");

                logger.warn("Email bounce detected: {} (type: {}, subType: {}, action: {}, status: {}, diagnostic: {})",
                        email, bounceType, bounceSubType, action, status, diagnosticCode);

                // 构建退信原因信息
                String reason = String.format("%s bounce (%s): %s - %s",
                        bounceType, bounceSubType, status, diagnosticCode);

                // 如果是硬退信，添加到黑名单
                if ("Permanent".equalsIgnoreCase(bounceType)) {
                    // 使用新的方法记录退信指标，包含邮箱和原因
                    emailMetrics.recordBounce("aws-ses", email, reason);

                    // 添加到黑名单
                    bouncedEmailService.addToBlacklist(email, reason);
                } else {
                    // 临时退信，只记录不加入黑名单
                    emailMetrics.recordBounce("aws-ses", email, reason);
                }
            }
        } catch (Exception e) {
            logger.error("Error processing bounce notification", e);
        }
    }

    /**
     * 处理投诉通知
     * @param message 通知内容
     */
    private void handleComplaint(JSONObject message) {
        try {
            JSONObject complaint = message.getJSONObject("complaint");

            // 记录所有投诉的收件人
            for (Object recipientObj : complaint.getJSONArray("complainedRecipients")) {
                JSONObject recipient = (JSONObject) recipientObj;
                String email = recipient.getString("emailAddress");

                logger.warn("Email complaint received from: {}", email);

                // 添加到黑名单
                bouncedEmailService.addToBlacklist(email, "Complaint received");
            }
        } catch (Exception e) {
            logger.error("Error processing complaint notification", e);
        }
    }

    /**
     * 处理成功投递通知
     * @param message 通知内容
     */
    private void handleDelivery(JSONObject message) {
        try {
            // 获取邮件发送相关信息
            JSONObject mail = message.getJSONObject("mail");
            String messageId = mail.getString("messageId");

            // 获取投递相关信息
            JSONObject delivery = message.getJSONObject("delivery");
            String timestamp = delivery.getString("timestamp");
            String smtpResponse = delivery.getString("smtpResponse");

            // 获取收件人列表
            JSONArray recipients = mail.getJSONArray("destination");

            // 处理每个收件人
            for (int i = 0; i < recipients.size(); i++) {
                String email = recipients.getString(i);

                // 将成功送达的邮箱添加到已验证列表，来源为"aws-ses-delivery"
                verifiedEmailService.addVerifiedEmail(email, "aws-ses-delivery");

                // 记录成功送达的指标
                // 使用现有的recordEmailSent方法
                emailMetrics.recordEmailSent("aws-ses", "delivery", true, System.currentTimeMillis(), email);

                logger.info("Email delivered successfully to: {} (messageId: {})", email, messageId);
            }
        } catch (Exception e) {
            logger.error("Error processing delivery notification", e);
        }
    }
}
