package com.media.mail.controller;

import com.media.mail.bean.SendSmsTemplateRequest;
import com.media.mail.bean.SendSnsRequest;
import com.media.mail.service.PhoneSnsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

@Slf4j
@RestController
@RequestMapping("/mail")
public class PhoneSendController {

    @Value(value = "${system.snsSend:true}")
    private Boolean isSendSns;

    @Autowired
    PhoneSnsService phoneSnsService;

    /**
     * 发送普通短信
     * @param request 短信请求
     * @return 响应结果
     */
    @PostMapping("/phone/send")
    public String send(@RequestBody SendSnsRequest request) {
        if(!isSendSns){
            log.info("config set do not send sns");
        }else {
            new Thread(() -> {
                try {
                    phoneSnsService.sendSns(request);
                    log.info("Success to send sns, request={}", request);
                } catch (Exception e) {
                    log.error("Failed to send sns, request={}, error={}", request, e.getMessage());
                }
            }).start();
        }
        return "phone sns send successfully";
    }

    /**
     * 发送模板短信
     * @param request 模板短信请求
     * @return 响应结果
     */
    @PostMapping("/phone/send/template")
    public ResponseEntity<Map<String, Object>> sendTemplate(@RequestBody SendSmsTemplateRequest request) {
        Map<String, Object> response = new HashMap<>();
        String requestId = UUID.randomUUID().toString();

        if(!isSendSns){
            log.info("config set do not send sns");
            response.put("success", false);
            response.put("message", "SMS sending is disabled by configuration");
            return ResponseEntity.ok(response);
        }

        // 立即返回响应，异步处理短信发送
        response.put("success", true);
        response.put("message", "SMS request accepted");
        response.put("requestId", requestId);

        // 异步执行短信发送
        CompletableFuture.runAsync(() -> {
            try {
                log.info("Async SMS sending started, requestId={}, request={}", requestId, request);
                boolean success = phoneSnsService.sendSmsTemplate(request);

                if (success) {
                    log.info("Async SMS sending succeeded, requestId={}", requestId);
                } else {
                    log.warn("Async SMS sending failed, requestId={}", requestId);
                }
            } catch (Exception e) {
                log.error("Error in async SMS sending, requestId={}, error={}", requestId, e.getMessage(), e);
            }
        });

        return ResponseEntity.accepted().body(response);
    }

}
