package com.media.mail.controller;

import com.media.mail.util.EmailMetricsCollector;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 邮件发送指标统计控制器
 */
@RestController
@RequestMapping("/mail/metrics")
public class EmailMetricsController {

    @Autowired
    private EmailMetricsCollector metricsCollector;

    /**
     * 获取当前邮件发送统计信息
     * @return 统计数据
     */
    @GetMapping
    public ResponseEntity<Map<String, Object>> getMetrics() {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("data", metricsCollector.getCurrentStats());
        return ResponseEntity.ok(response);
    }

    /**
     * 重置统计数据
     * @return 操作结果
     */
    @PostMapping("/reset")
    public ResponseEntity<Map<String, Object>> resetMetrics() {
        metricsCollector.resetStats();

        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "Email metrics have been reset");
        return ResponseEntity.ok(response);
    }
}
