-- 创建已验证邮箱表
CREATE TABLE IF NOT EXISTS `verified_email` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `email` varchar(255) NOT NULL COMMENT '邮箱地址',
  `domain` varchar(255) NOT NULL COMMENT '域名',
  `source` varchar(50) DEFAULT 'manual' COMMENT '验证来源',
  `verified_at` datetime NOT NULL COMMENT '首次验证时间',
  `last_verified_at` datetime NOT NULL COMMENT '最后验证时间',
  `verify_count` int(11) DEFAULT 1 COMMENT '验证次数',
  `expires_at` datetime DEFAULT NULL COMMENT '过期时间',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_email` (`email`),
  KEY `idx_domain` (`domain`),
  KEY `idx_expires_at` (`expires_at`),
  KEY `idx_verified_at` (`verified_at`),
  KEY `idx_last_verified_at` (`last_verified_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='已验证邮箱表';
