CREATE TABLE sms_channel_history (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    phone VARCHAR(32) NOT NULL COMMENT '手机号',
    prefix VARCHAR(8) NOT NULL COMMENT '手机号前缀，例如+86,+1等',
    channel_type INT NOT NULL COMMENT '通道类型：1-Submail, 2-拉力',
    send_time DATETIME NOT NULL COMMENT '发送时间',
    success BOOLEAN NOT NULL COMMENT '是否发送成功',
    INDEX idx_phone_send_time (phone, send_time DESC),
    INDEX idx_prefix_success (prefix, success, send_time DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='短信通道发送历史';
