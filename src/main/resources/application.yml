server:
  port: 8080
  servlet:
    context-path: /

system:
  emailSend: ${SYSTEM_EMAIL_SEND_FLAG:true}
  snsSend: ${SYSTEM_SNS_SEND_FLAG:true}
  sms:
    channel:
      submail-enabled: ${SMS_SUBMAIL_ENABLED:true}
      lali-enabled: ${SMS_LALI_ENABLED:true}
      runxin-enabled: ${SMS_RUNXIN_ENABLED:false}
      nxcloud-enabled: ${SMS_NXCLOUD_ENABLED:false}
      zhenyi-enabled: ${SMS_ZHENYI_ENABLED:false}
      default-channel: ${SMS_DEFAULT_CHANNEL:1}
      switch-window-minutes: ${SMS_SWITCH_WINDOW_MINUTES:15}
    # 润信短信配置
    runxin:
      appkey: ${RUNXIN_SMS_APPKEY:}
      appcode: ${RUNXIN_SMS_APPCODE:}
      appsecret: ${RUNXIN_SMS_APPSECRET:}
      url: ${RUNXIN_SMS_URL:http://47.238.243.213:9090/sms/batch/v2}
    # 牛信短信配置
    nxcloud:
      access-key: ${NXCLOUD_SMS_ACCESS_KEY:}
      access-secret: ${NXCLOUD_SMS_ACCESS_SECRET:}
      app-key: ${NXCLOUD_SMS_APP_KEY:}
      url: ${NXCLOUD_SMS_URL:https://api.nxcloud.com/v1/sms/mt}
    # 振亿短信配置
    zhenyi:
      account: ${ZHENYI_SMS_ACCOUNT:}
      password: ${ZHENYI_SMS_PASSWORD:}
      url: ${ZHENYI_SMS_URL:http://域名:8080/sms/v2/send-different}
  # 手机号黑名单相关配置
  phone:
    blacklist:
      enabled: ${PHONE_BLACKLIST_ENABLED:true}
      failure-threshold: ${SMS_FAILURE_THRESHOLD:3}
      duration-days: ${SMS_BLACKLIST_DURATION_DAYS:7}
    validation:
      enabled: ${PHONE_VALIDATION_ENABLED:true}

spring:
  application:
    name: "mail"
  # 数据库配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ${MYSQL_URL:************************************************************************************************************************************************************************************************}
    username: ${MYSQL_USERNAME:envdev-user}
    password: ${MYSQL_PASSWORD:k6xetf4YkB}
    druid:
      # 公共配置
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      filters: stat,wall
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        login-username: admin
        login-password: admin
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"

# MyBatis-Plus配置
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.media.mail.entity
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

app:
  id: ${spring.application.name}

# AWS配置
aws:
  sqs:
    region: ${SES_REGION:ap-southeast-1}
    # 消息处理配置
    concurrency: ${SQS_CONCURRENCY:5}
    batch-size: ${SQS_BATCH_SIZE:10}
    visibility-timeout: ${SQS_VISIBILITY_TIMEOUT:30}
    poll-interval-ms: ${SQS_POLL_INTERVAL_MS:1000}
    long-polling-seconds: ${SQS_LONG_POLLING_SECONDS:20}
    endpoint-override: ${SQS_ENDPOINT_OVERRIDE:}

apollo:
  meta: ${APOLLO_META:http://apollo-config-test.xme.world}
  cluster: ${APOLLO_CLUSTER:dev}
  bootstrap:
    enabled: true
    namespaces: ${APOLLO_NAMESPACE:application}

management:
  endpoints:
    web:
      exposure:
        include: "prometheus,info,liveness,readiness,health"
  endpoint:
    health:
      probes:
        enabled: true
  metrics:
    tags:
      application: mail
    distribution:
      slo[http.server.requests]: 5ms,10ms,25ms,50ms,75ms,100ms,125ms,150ms,175ms,200ms,225ms,250ms,300ms,350ms,400ms,450ms,500ms,750ms,1s,1500ms,2s,2500ms,3s,5s
  server:
    port: 9090
    base-path: /

# 日志配置
logging:
  level:
    root: INFO
    com.media.mail: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
