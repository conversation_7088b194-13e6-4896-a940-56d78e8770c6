-- 创建黑名单手机号表
CREATE TABLE IF NOT EXISTS blacklisted_phone (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    phone_number VARCHAR(20) NOT NULL,
    country_code VARCHAR(10) NOT NULL,
    reason VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    permanent BOOLEAN DEFAULT TRUE,
    expires_at TIMESTAMP NULL,
    created_by VARCHAR(100),
    notes TEXT,
    failure_count INT DEFAULT 0,
    deleted INT DEFAULT 0,
    UNIQUE KEY uk_phone_country (phone_number, country_code)
);

-- 创建短信发送统计表
CREATE TABLE IF NOT EXISTS sms_send_stats (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    provider VARCHAR(50) NOT NULL,
    sms_type VARCHAR(20) NOT NULL,
    status VARCHAR(20) NOT NULL,
    recipient VARCHAR(20) NOT NULL,
    country_code VARCHAR(10) NOT NULL,
    send_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processing_time BIGINT,
    retry_count INT DEFAULT 0,
    error_message TEXT,
    content_length INT,
    message_id VARCHAR(100),
    template_id VARCHAR(100),
    deleted INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_recipient (recipient, country_code),
    INDEX idx_send_time (send_time),
    INDEX idx_status (status)
);

-- 创建黑名单邮箱表（如果不存在）
CREATE TABLE IF NOT EXISTS blacklisted_email (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(255) NOT NULL,
    reason VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    permanent BOOLEAN DEFAULT TRUE,
    expires_at TIMESTAMP NULL,
    created_by VARCHAR(100),
    notes TEXT,
    failure_count INT DEFAULT 0,
    deleted INT DEFAULT 0,
    UNIQUE KEY uk_email (email)
);

-- 创建邮件发送统计表（如果不存在）
CREATE TABLE IF NOT EXISTS email_send_stats (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    provider VARCHAR(50) NOT NULL,
    email_type VARCHAR(20) NOT NULL,
    status VARCHAR(20) NOT NULL,
    recipient VARCHAR(255) NOT NULL,
    subject VARCHAR(255),
    send_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processing_time BIGINT,
    retry_count INT DEFAULT 0,
    error_message TEXT,
    message_id VARCHAR(100),
    deleted INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_recipient (recipient),
    INDEX idx_send_time (send_time),
    INDEX idx_status (status)
);

-- 添加初始数据（可选）
INSERT INTO blacklisted_phone (phone_number, country_code, reason, permanent, created_by)
SELECT '**********', '86', '测试黑名单手机号', TRUE, 'system'
WHERE NOT EXISTS (SELECT 1 FROM blacklisted_phone WHERE phone_number = '**********' AND country_code = '86');
