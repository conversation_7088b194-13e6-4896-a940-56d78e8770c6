package com.media.mail;

import com.media.mail.subsms.ZhenyiSmsHttpClient;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 振亿短信测试类
 */
@SpringBootTest
public class ZhenyiSmsTest {

    @Autowired
    private ZhenyiSmsHttpClient zhenyiSmsHttpClient;

    @Test
    public void testConfigStatus() {
        System.out.println("振亿短信配置状态: " + zhenyiSmsHttpClient.getConfigStatus());
    }

    @Test
    public void testSendSingleSms() {
        // 测试发送单条短信
        boolean result = zhenyiSmsHttpClient.sendSingleSms("13800138000", "测试短信内容");
        System.out.println("单条短信发送结果: " + result);
    }

    @Test
    public void testSendBatchSms() {
        // 测试批量发送相同内容短信
        List<String> phones = Arrays.asList("13800138000", "13800138001", "13800138002");
        boolean result = zhenyiSmsHttpClient.sendBatchSms(phones, "批量测试短信内容");
        System.out.println("批量短信发送结果: " + result);
    }

    @Test
    public void testSendBatchSmsWithDifferentContent() {
        // 测试批量发送不同内容短信
        List<String> phones = Arrays.asList("13800138000", "13800138001", "13800138002");
        List<String> messages = Arrays.asList("短信内容1", "短信内容2", "短信内容3");
        boolean result = zhenyiSmsHttpClient.sendBatchSmsWithDifferentContent(phones, messages);
        System.out.println("不同内容批量短信发送结果: " + result);
    }

    @Test
    public void testSendTemplateSms() {
        // 测试模板短信发送
        Map<String, String> templateVars = new HashMap<>();
        templateVars.put("code", "123456");
        templateVars.put("time", "5");

        boolean result = zhenyiSmsHttpClient.sendTemplateSms("13800138000", templateVars, "chinese");
        System.out.println("中文模板短信发送结果: " + result);

        result = zhenyiSmsHttpClient.sendTemplateSms("13800138000", templateVars, "english");
        System.out.println("英文模板短信发送结果: " + result);
    }

    @Test
    public void testSendBatchTemplateSms() {
        // 测试批量模板短信发送
        List<String> phones = Arrays.asList("13800138000", "13800138001", "13800138002");
        Map<String, String> templateVars = new HashMap<>();
        templateVars.put("code", "123456");
        templateVars.put("time", "5");

        boolean result = zhenyiSmsHttpClient.sendBatchTemplateSms(phones, templateVars, "chinese");
        System.out.println("批量中文模板短信发送结果: " + result);
    }
}
