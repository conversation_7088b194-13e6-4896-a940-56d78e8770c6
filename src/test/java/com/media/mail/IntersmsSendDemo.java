package com.media.mail;

import com.media.mail.subsms.CnSmsHttpClient;
import com.media.mail.subsms.InterSmsHttpClient;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;



@SpringBootTest
public class IntersmsSendDemo {

    @Autowired
    InterSmsHttpClient interSmsHttpClient;

    @Autowired
    CnSmsHttpClient cnSmsHttpClient;

    @Test
    void interSendByContent() {
        String to = "+85269xxxxxx";
        String content = "【XME】你好，你的验证码是1234";
        interSmsHttpClient.sendByContent(to, content);
    }

    @Test
    void cnSendByContent() {
        String to = "133xxxxxxxx";
        String content = "【信诚正业】您的短信验证码为: 778323，有效期 5 分钟。如非您本人操作请忽略。";
        cnSmsHttpClient.sendByContent(to, content);
    }

}
