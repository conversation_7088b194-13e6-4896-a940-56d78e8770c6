//package com.example.mail;
//
//import com.alibaba.fastjson.JSONObject;
//import com.example.mail.subsms.InterSmsHttpClient;
//import org.junit.jupiter.api.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//
//@SpringBootTest
//public class IntersmsXsendDemo {
//
//    @Autowired
//    InterSmsHttpClient interSmsHttpClient;
//
//    @Test
//    void sendByProject() {
//        String to = "+85269493938";
//        String project = "PVniJ";
//        JSONObject vars = new JSONObject();
//        vars.put("code", "3344");
//        interSmsHttpClient.sendByProject(to, project, vars.toJSONString());
//        //sb.substring(0, sb.length() - 1) = appid=64512&project=PVniJ&sign_type=md5&sign_version=2&timestamp=1743348886&to=+85269493938
//        //2025-03-30 23:34:46 - 生成签名:645125c6380f83f13cffb1b40a523d703aacdappid=64512&project=PVniJ&sign_type=md5&sign_version=2&timestamp=1743348886&to=+85269493938645125c6380f83f13cffb1b40a523d703aacd
//        //{"status":"success","send_id":"b813bd38e6344db0585ed5f592aaf61a","fee":1.191}
//    }
//
//}
