package com.media.mail;

import com.media.mail.subsms.ZhenyiSmsHttpClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 振亿短信使用示例
 * 演示如何使用振亿短信客户端发送各种类型的短信
 */
@Component
public class ZhenyiSmsDemo implements CommandLineRunner {

    @Autowired
    private ZhenyiSmsHttpClient zhenyiSmsHttpClient;

    @Override
    public void run(String... args) throws Exception {
        // 检查配置状态
        System.out.println("=== 振亿短信配置状态 ===");
        System.out.println(zhenyiSmsHttpClient.getConfigStatus());
        System.out.println();

        // 如果配置不完整，跳过演示
        if (!zhenyiSmsHttpClient.isConfigValid()) {
            System.out.println("振亿短信配置不完整，跳过演示");
            return;
        }

        // 演示单条短信发送
        System.out.println("=== 单条短信发送演示 ===");
        boolean singleResult = zhenyiSmsHttpClient.sendSingleSms("13800138000", "这是一条测试短信");
        System.out.println("单条短信发送结果: " + singleResult);
        System.out.println();

        // 演示批量相同内容短信发送
        System.out.println("=== 批量相同内容短信发送演示 ===");
        List<String> phones = Arrays.asList("13800138000", "13800138001", "13800138002");
        boolean batchResult = zhenyiSmsHttpClient.sendBatchSms(phones, "这是一条批量测试短信");
        System.out.println("批量短信发送结果: " + batchResult);
        System.out.println();

        // 演示批量不同内容短信发送
        System.out.println("=== 批量不同内容短信发送演示 ===");
        List<String> messages = Arrays.asList("短信内容1", "短信内容2", "短信内容3");
        boolean differentResult = zhenyiSmsHttpClient.sendBatchSmsWithDifferentContent(phones, messages);
        System.out.println("不同内容批量短信发送结果: " + differentResult);
        System.out.println();

        // 演示中文模板短信发送
        System.out.println("=== 中文模板短信发送演示 ===");
        Map<String, String> chineseVars = new HashMap<>();
        chineseVars.put("code", "123456");
        chineseVars.put("time", "5");
        boolean chineseResult = zhenyiSmsHttpClient.sendTemplateSms("13800138000", chineseVars, "chinese");
        System.out.println("中文模板短信发送结果: " + chineseResult);
        System.out.println();

        // 演示英文模板短信发送
        System.out.println("=== 英文模板短信发送演示 ===");
        Map<String, String> englishVars = new HashMap<>();
        englishVars.put("code", "654321");
        englishVars.put("time", "10");
        boolean englishResult = zhenyiSmsHttpClient.sendTemplateSms("13800138000", englishVars, "english");
        System.out.println("英文模板短信发送结果: " + englishResult);
        System.out.println();

        // 演示批量模板短信发送
        System.out.println("=== 批量模板短信发送演示 ===");
        boolean batchTemplateResult = zhenyiSmsHttpClient.sendBatchTemplateSms(phones, chineseVars, "chinese");
        System.out.println("批量模板短信发送结果: " + batchTemplateResult);
        System.out.println();

        System.out.println("=== 振亿短信演示完成 ===");
    }
}
