## 发送注册邮件的服务

### 开发与部署示范

- 编译jar包：mvn clean package -DskipTests
- 编译Docker镜像：docker build . -t slpool.xyz:5000/mail:20240813100400
- 上传Docker镜像：docker push slpool.xyz:5000/mail:20240731170000
- 打开Rancher，选择xee集群，选择Worklads。https://slpool.xyz:8443/dashboard/c/c-m-lk5zqmcz/explorer/apps.deployment
- 选择对应的Deployment，点击右边三个点，选择Edit Config，然后在Image中更新Container Image，Save即可
- 

### 查看日志

- 打开Rancher，选择xee集群，选择Pods。
- 选择对应的Pod，点击右边三个点，选择View Logs，点击右下方的Download即可。

### application配置

Springboot的配置由于本地开发环境与测试环境的差异，变量以如下形式配置: username: ${环境变量值:默认值}

```
url: ${MYSQL_URL:*******************************************************************************************************************************************}
username: ${MYSQL_USERNAME:root}
password: ${MYSQL_PASSWORD:password}
```
然后在Rancher中，设置环境变量为：
MYSQL_URL：${测试环境变量 值}