# 振亿短信集成文档

## 概述

振亿短信通道已成功集成到邮件系统中，支持单条和批量短信发送功能。

## 功能特性

- ✅ 单条短信发送
- ✅ 批量相同内容短信发送
- ✅ 批量不同内容短信发送
- ✅ 模板短信发送（中文/英文）
- ✅ 批量模板短信发送
- ✅ 事务ID幂等处理
- ✅ MD5签名验证
- ✅ 失败重试机制
- ✅ 详细日志记录

## 配置说明

### 环境变量配置

在 `application.yml` 或环境变量中配置以下参数：

```yaml
system:
  sms:
    channel:
      zhenyi-enabled: ${SMS_ZHENYI_ENABLED:false}  # 是否启用振亿通道
    zhenyi:
      account: ${ZHENYI_SMS_ACCOUNT:}      # 振亿账户
      password: ${ZHENYI_SMS_PASSWORD:}    # 振亿密码
      url: ${ZHENYI_SMS_URL:http://域名:8080/sms/v2/send-different}  # API地址
```

### 必需的环境变量

```bash
# 振亿短信配置
ZHENYI_SMS_ACCOUNT=your_account
ZHENYI_SMS_PASSWORD=your_password
ZHENYI_SMS_URL=http://your-domain:8080/sms/v2/send-different
SMS_ZHENYI_ENABLED=true
```

## API接口说明

### 请求格式

```json
{
  "account": "账户名",
  "transactionId": "事务ID",
  "password": "MD5签名",
  "list": [
    {
      "mobile": "手机号",
      "content": "短信内容",
      "uuid": "唯一标识",
      "ext": "扩展码"
    }
  ]
}
```

### 响应格式

成功响应：
```json
{
  "success": true,
  "transactionId": "事务ID",
  "failList": null
}
```

失败响应：
```json
{
  "success": false,
  "transactionId": "事务ID",
  "failList": [
    {
      "mobile": "手机号",
      "errorCode": "错误码",
      "errorDesc": "错误描述",
      "uuid": "唯一标识"
    }
  ]
}
```

## 使用方法

### 1. 单条短信发送

```java
@Autowired
private ZhenyiSmsHttpClient zhenyiSmsHttpClient;

// 发送单条短信
boolean result = zhenyiSmsHttpClient.sendSingleSms("***********", "测试短信内容");
```

### 2. 批量相同内容短信发送

```java
List<String> phones = Arrays.asList("***********", "***********", "***********");
boolean result = zhenyiSmsHttpClient.sendBatchSms(phones, "批量测试短信内容");
```

### 3. 批量不同内容短信发送

```java
List<String> phones = Arrays.asList("***********", "***********", "***********");
List<String> messages = Arrays.asList("短信内容1", "短信内容2", "短信内容3");
boolean result = zhenyiSmsHttpClient.sendBatchSmsWithDifferentContent(phones, messages);
```

### 4. 模板短信发送

```java
Map<String, String> templateVars = new HashMap<>();
templateVars.put("code", "123456");
templateVars.put("time", "5");

// 中文模板
boolean result = zhenyiSmsHttpClient.sendTemplateSms("***********", templateVars, "chinese");

// 英文模板
boolean result = zhenyiSmsHttpClient.sendTemplateSms("***********", templateVars, "english");
```

### 5. 批量模板短信发送

```java
List<String> phones = Arrays.asList("***********", "***********", "***********");
Map<String, String> templateVars = new HashMap<>();
templateVars.put("code", "123456");
templateVars.put("time", "5");

boolean result = zhenyiSmsHttpClient.sendBatchTemplateSms(phones, templateVars, "chinese");
```

## 签名算法

振亿短信使用MD5签名验证：

```java
// 签名算法：MD5(account + password + transactionId)
String signStr = account + password + transactionId;
String signature = DigestUtils.md5DigestAsHex(signStr.getBytes());
```

## 限制说明

- 单次批量发送最多支持1000个手机号
- 短信内容最大长度1000字符
- 事务ID必须唯一，用于幂等处理
- 支持中文和英文模板

## 错误处理

系统会自动处理以下错误情况：

1. **配置错误**：检查账户、密码、URL配置
2. **参数错误**：验证手机号格式、内容长度
3. **网络错误**：HTTP请求超时或连接失败
4. **业务错误**：解析API响应中的错误码和错误信息

## 日志记录

系统会记录详细的日志信息：

- 请求参数和响应结果
- 错误信息和异常堆栈
- 发送成功/失败统计
- 配置状态检查

## 测试

运行测试用例验证功能：

```bash
# 运行振亿短信测试
mvn test -Dtest=ZhenyiSmsTest
```

## 监控指标

系统集成了以下监控指标：

- 短信发送成功率
- 发送延迟统计
- 错误码分布
- 配置状态检查

## 注意事项

1. 请确保配置正确的API地址和认证信息
2. 事务ID必须唯一，避免重复发送
3. 手机号格式需要符合国际标准
4. 短信内容需要符合运营商规范
5. 建议在生产环境使用HTTPS协议

## 支持

如有问题，请联系开发团队或查看系统日志获取详细信息。 