# 润信短信渠道集成文档

## 概述

润信短信渠道已集成到短信发送系统中，作为第三个可选的短信发送渠道。润信支持批量短信发送，特别适合大量短信发送场景。

## 配置项

### Apollo配置项

```properties
# 润信渠道开关
SMS_RUNXIN_ENABLED=false

# 润信API配置
RUNXIN_SMS_APPKEY=你的AppKey
RUNXIN_SMS_APPCODE=你的AppCode  
RUNXIN_SMS_APPSECRET=你的AppSecret
RUNXIN_SMS_URL=http://**************:9090/sms/batch/v2

# 渠道优先级配置（可选）
SMS_PREFIX_CHANNEL_MAP=+1=3,+44=3,+81=3
SMS_DEFAULT_CHANNEL=3
```

### 渠道ID说明

- 1: Submail（国际短信主要渠道）
- 2: 拉力（国际短信备用渠道）
- 3: 润信（批量短信渠道）**新增**

## API接口

### 1. 查看渠道状态

```http
GET /sms/channel/status
```

响应示例：
```json
{
  "channels": {
    "runxin": {
      "id": 3,
      "name": "润信",
      "enabled": true,
      "description": "批量短信渠道",
      "configStatus": "润信短信配置完整"
    }
  },
  "availableChannels": [1, 2, 3],
  "config": {
    "defaultChannel": 1,
    "defaultChannelName": "Submail"
  }
}
```

### 2. 获取推荐渠道

```http
GET /sms/channel/suggest?phone=1234567890&prefix=+1
```

### 3. 测试润信配置

```http
POST /sms/channel/test/runxin
```

### 4. 发送模板短信

```http
POST /mail/phone/send/template
```

请求体：
```json
{
  "phonePrefix": "+1",
  "phone": "1234567890",
  "useEnglishTemplate": true,
  "variables": {
    "code": "123456",
    "time": "5"
  }
}
```

## 使用方式

### 1. 启用润信渠道

```properties
SMS_RUNXIN_ENABLED=true
```

### 2. 配置API参数

根据润信提供的API文档配置相应参数：

```properties
RUNXIN_SMS_APPKEY=你的AppKey
RUNXIN_SMS_APPCODE=你的AppCode
RUNXIN_SMS_APPSECRET=你的AppSecret
```

### 3. 设置渠道优先级（可选）

可以通过前缀映射设置特定地区号码优先使用润信：

```properties
# 美国号码优先使用润信
SMS_PREFIX_CHANNEL_MAP=+1=3
```

### 4. 设置为默认渠道（可选）

```properties
SMS_DEFAULT_CHANNEL=3
```

## 模板支持

润信渠道支持中英文模板：

### 中文模板
```
您的验证码是：${code}，有效期${time}分钟，请勿泄露给他人。
```

### 英文模板
```
Your verification code is: ${code}. Valid for ${time} minutes. Please do not share this code with others.
```

## 特性

1. **批量发送**: 支持一次发送最多1000个号码
2. **模板支持**: 支持中英文模板
3. **智能切换**: 根据发送历史智能选择渠道
4. **配置热更新**: 支持Apollo配置热更新
5. **错误处理**: 完善的错误处理和日志记录

## 监控

可以通过以下接口监控润信渠道状态：

- `/sms/channel/status` - 查看渠道状态
- `/sms/channel/stats` - 查看统计信息
- `/sms/channel/test/runxin` - 测试润信配置

## 注意事项

1. 润信渠道默认关闭，需要手动启用
2. 必须配置完整的API参数才能正常使用
3. 润信主要用于批量发送，单条发送也支持但建议优先使用其他渠道
4. 发送失败时会自动切换到其他可用渠道

## 故障排除

### 配置检查

使用测试接口检查配置：
```http
POST /sms/channel/test/runxin
```

### 常见问题

1. **配置不完整**: 检查APPKEY、APPCODE、APPSECRET是否正确配置
2. **渠道未启用**: 确认SMS_RUNXIN_ENABLED=true
3. **网络问题**: 检查服务器是否能访问润信API地址
4. **参数格式**: 确认手机号和消息内容格式正确

## 版本历史

- v1.0.0: 初始版本，支持基本的短信发送功能
- 支持批量发送（最多1000个号码）
- 支持中英文模板
- 集成到现有的短信渠道选择逻辑中 